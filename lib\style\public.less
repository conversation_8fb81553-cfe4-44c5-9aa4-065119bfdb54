.border(@size: 0.25,
    @left: 0,
    @right: 0,
    @bgColor: var(--emui_color_divider_horizontal)) {
    position: relative;

    &::after {
        content: '';
        display: block;
        height: 1px;
        position: absolute;
        bottom: 0;
        left: @left;
        right: @right;
        background-color: @bgColor;
        transform: scaleY(@size);
    }
}

.borderTop(@size: 0.25,
    @left: 0,
    @right: 0,
    @bgColor: var(--emui_color_divider_horizontal)) {
    position: relative;

    &::after {
        content: '';
        display: block;
        height: 1px;
        position: absolute;
        top: 0;
        left: @left;
        right: @right;
        background-color: @bgColor;
        transform: scaleY(@size);
    }
}

.borderR(@size: 0.25,
    @top: 50%,
    @right: 0,
    @height: 100%,
    @color: var(--emui_color_divider_horizontal)) {
    position: relative;

    &::after {
        content: '';
        position: absolute;
        top: @top;
        right: @right;
        width: 1px;
        height: @height;
        background-color: @color;
        transform: translateY(-50%) scaleX(@size);
    }
}

.appear() {
    @keyframes appear {
        0% {
            bottom: -100%;
        }

        to {
            bottom: 0.4rem;
        }
    }
}

.mask(@bgColor: var(--emui_mask_thin)) {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: @bgColor;
    z-index: 10;
}

.active(@radius: var(--emui_corner_radius_clicked),
    @color: var(--emui_clickeffic_default_color)) {
    &:not(.disable) {
        &:active {
            background-color: @color;
            border-radius: @radius;
        }
    }
}

.btn(@color: var(--emui_accent),
    @bgColor: var(--emui_color_list_divider_light),
    @bgActiveColor: var(--emui_clickeffic_default_color)) {
    .btn {
        width: 100%;
        padding: 1.6rem 2.4rem 2.4rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 9;
        background-color: var(--emui_color_subbg);

        .disable {
            opacity: 0.38;
        }

        .btn-box {
            width: 18rem;
            height: 4rem;
            background-color: @bgColor;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: var(--emui_text_size_button1);
            color: @color;
            border-radius: 2rem;
            .medium();

            &:not(.disable) {
                &:active {
                    background-color: @bgActiveColor;
                }
            }
        }
    }

    .pad {
        .add {
            .add-box {
                width: var(--pad_column_3);
            }
        }
    }
}

.fade() {

    &.com-fade-enter,
    &.com-fade-leave-active {
        opacity: 0;
    }

    &.com-fade-enter-active,
    &.com-fade-leave-active {
        transition: all 0.3s ease-in-out;
    }
}

.move(@height: 31.6rem) {

    &.com-move-enter,
    &.com-move-leave-active {
        transform: translate3d(0, @height, 0);
    }

    &.com-move-enter-active,
    &.com-move-leave-active {
        transition: all 0.3s ease-in-out;
    }
}

.spread() {

    &.fade-enter,
    &.fade-leave-active {
        height: 0;
    }

    &.fade-enter-active,
    &.fade-leave-active {
        transition: all 0.3s ease;
    }
}

.medium() {
    font-family: "PingFangSC-Medium";
}

.cardStyle {
    margin: 0 var(--emui_dimens_card_start) var(--emui_dimens_card_middle) var(--emui_dimens_card_end);
    padding: 0 var(--emui_dimens_card_inner_end) 0 var(--emui_dimens_card_inner_start);
    background-color: var(--emui_card_panel_bg);
    border-radius: var(--emui_corner_radius_large);
}

.color() {
    .green {
        background-color: var(--emui_color_connected);
    }

    .yellow {
        background-color: var(--emui_color_warning);
    }

    .red {
        background-color: var(--emui_functional_red);
    }
}

.dialogMask {
    .mask()
}

.dialog {
    .mask()
}

.dialog-container {
    position: fixed;
    bottom: 1.6rem;
    left: 1.2rem;
    right: 1.2rem;
    background: var(--emui_popup_bg);
    border-radius: 2.4rem;
    z-index: 111;
    transition: all 0.5s;
    -webkit-animation: actionsheet 400ms ease-in-out 0ms;
    animation: actionsheet 400ms ease-in-out 0ms;
    color: var(--emui_text_primary);

    .title {
        font-size: 2rem;
        font-weight: normal;
        height: 2.6rem;
        line-height: 2.6rem;
        padding: 1.5rem 2.4rem 1.5rem 2.4rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .mod-head {
        padding: 1.5rem 2.4rem 1.5rem 2.4rem;
        display: flex;
        flex-flow: column;

        .title {
            padding: 0px;
        }

        .subtitle {
            color: var(--emui_text_secondary);
            font-size: 1.4rem;
        }
    }

    >p {
        line-height: 2.2rem;
        font-size: 1.6rem;
        margin: 0px 2.4rem;
        color: var(--emui_text_primary);
    }
}

.pad .dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0px 1.2rem;
}

.pad .dialog-container {
    width: var(--pad_column_4);
    position: relative;
    bottom: 0px;
    left: 0px;
    right: 0px;
}

.tahiti .dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0px;
}

.tahiti .dialog-container {
    width: var(--pad_column_4);
    position: relative;
    bottom: 0px;
    left: 0px;
    right: 0px;
}

@keyframes actionsheet {
    0% {
        bottom: -100%;
    }

    to {
        bottom: 1rem;
    }
}

.dialog-btns {
    display: flex;
    color: var(--emui_accent);
    font-size: 1.6rem;
    height: 5.6rem;
    justify-content: center;
    text-align: center;
    line-height: 5.6rem;
    padding: 0px 1.6rem;
    margin-top: 0.8rem;

    .line {
        width: 1px;
        height: 2.4rem !important;
        position: relative;
        margin-top: 0.8rem;
        transform: scaleX(0.25);
        background-color: var(--emui_color_divider_horizontal);
    }

    >div {
        flex: 1;
        font-weight: 400;
        height: 4rem;
        width: 14.4rem;
        font-size: 1.6rem;
        text-align: center;
        line-height: 4rem;

        >p {
            width: 100%;
            height: 4rem;
            line-height: 4rem;
            border-radius: 0.3rem;
        }

        .red {
            color: var(--emui_functional_red);
        }
    }
}

//信息快样式
.block-info {
    display: flex;
    flex-flow: column;
    align-items: center;

    >div {
        font-weight: 400;
        display: flex;
        align-items: baseline;
        color: var(--emui_text_primary);
        font-size: 2.2rem;

        >span {
            color: var(--emui_text_secondary);
            font-size: 1.2rem;
            margin-left: 0.2rem;
        }
    }

    >span {
        margin-top: 0.2rem;
        font-size: 1.2rem;
        color: var(--emui_text_secondary);
    }
}

.info-item {
    flex: 1;
    .borderR(@height: 2.4rem);

    &:last-child {
        &:after {
            display: none;
        }
    }
}

.mod-tips-left {
    position: absolute;
    z-index: 10;
    left: -9em;
    bottom: 2.4rem;

    .content {
        padding: 1.2rem 1.6rem;
        box-shadow: 0px 1rem 5rem 1px var(--emui_dropbox_bg_shadow);
        color: var(--emui_tip_text);
        font-size: 1.4rem;
        border-radius: 1.6rem;
        background-color: var(--emui_tip_bg);
        width: 14rem;
        white-space: pre-wrap;
        max-width: 14rem;
        word-break: break-word;
    }

    & > .triangle {
        margin-left: 9rem;
        width: 0;
        height: 0;
        margin-top: -1px;
        border-left: 0.8rem solid transparent;
        border-right: 0.8rem solid transparent;
        border-top: 0.6rem solid var(--emui_tip_bg);
    }
}

.mod-tips-right {
    position: absolute;
    z-index: 10;
    right: -8rem;
    bottom: 2.4rem;
    text-align: left;

    .content {
        padding: 1.2rem 1.6rem;
        box-shadow: 0px 1rem 5rem 1px var(--emui_dropbox_bg_shadow);
        color: var(--emui_tip_text);
        font-size: 1.4rem;
        border-radius: 1.6rem;
        background-color: var(--emui_tip_bg);
        white-space: pre-wrap;
        width: 14rem;
        word-break: break-word;
    }

    & > .triangle {
        float: right;
        margin-right: 8rem;
        width: 0;
        height: 0;
        margin-top: -1px;
        border-left: 0.8rem solid transparent;
        border-right: 0.8rem solid transparent;
        border-top: 0.6rem solid var(--emui_tip_bg);
    }
}

.titlebar-overlay {
    width: 100%;
    position: fixed;
    top: 0px;
    left: 0px;
    background-color: var(--emui_color_subbg);
    z-index: 2;
}
