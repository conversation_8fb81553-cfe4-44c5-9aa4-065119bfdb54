export function initState(profile) {
    let states = {};
    for (let key in profile) {
        states[key] = {};
        let item = profile[key];
        if (Array.isArray(item)) {
            item.forEach((arrayItem, index) => {
                states[key][arrayItem.name] = arrayItem.default;
            });
        } else {
            states[key][item.name] = item.default;
        }
    }
    return states;
}

export function handleState(profile, state, result) {
    if (
        !result ||
        !result.sid ||
        !result.data
    ) {
        return;
    }

    let sid = result.sid;

    for (let key in profile) {
        if (key !== sid) {
            continue;
        }

        let data = result.data;
        for (let char in data) {
            state[sid][char] = data[char];
        }
    }
}

