<template>
  <div id="product-wiki-page">
    <Titlebar
      :title="$t('product_wiki')"
      :showRightIcon="false"
      @leftClick="$router.goBack()"
    />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      <!-- 产品图片展示区域 -->
      <div class="product-image-section">
        <img
          class="product-image"
          :src="require('../assets/product.png')"
          :alt="$t('product_wiki')"
        />
      </div>

      <!-- 使用指南区域 -->
      <div class="guide-section">
        <div class="section-title">{{ $t('wiki_guide_title') }}</div>
        <div class="guide-grid">
          <div class="guide-item" @click="handleNavClick('quick_start')">
            <div class="guide-icon">
              <img :src="require('../assets/ic_Timer_on.png')" alt="快速入门" />
            </div>
            <div class="guide-name">{{ $t('wiki_quick_start') }}</div>
          </div>
          <div class="guide-item" @click="handleNavClick('usage_tips')">
            <div class="guide-icon">
              <img :src="require('../assets/ic_ScreenOn_on.png')" alt="使用技巧" />
            </div>
            <div class="guide-name">{{ $t('wiki_usage_tips') }}</div>
          </div>
          <div class="guide-item" @click="handleNavClick('air_quality')">
            <div class="guide-icon">
              <img :src="require('../assets/ic_ScreenOn_on.png')" alt="空气质量百科" />
            </div>
            <div class="guide-name">{{ $t('wiki_air_quality') }}</div>
          </div>
          <div class="guide-item" @click="handleNavClick('troubleshooting')">
            <div class="guide-icon">
              <img :src="require('../assets/icon_service_on.png')" alt="故障排除" />
            </div>
            <div class="guide-name">{{ $t('wiki_troubleshooting') }}</div>
          </div>
        </div>
      </div>

      <!-- 大家都在问区域 -->
      <div class="faq-section">
        <div class="section-title">{{ $t('wiki_faq_title') }}</div>
        <ListContainer style="width: auto">
          <ListItemNav
            :name="$t('wiki_faq_1')"
            @click="handleNavClick('faq_1')"
          />
          <ListItemNav
            :name="$t('wiki_faq_2')"
            @click="handleNavClick('faq_2')"
          />
          <ListItemNav
            :name="$t('wiki_faq_3')"
            @click="handleNavClick('faq_3')"
          />
          <ListItemNav
            :name="$t('wiki_faq_4')"
            @click="handleNavClick('faq_4')"
          />
          <ListItemNav
            :name="$t('wiki_faq_5')"
            @click="handleNavClick('faq_5')"
          />
          <ListItemNav
            :name="$t('wiki_faq_6')"
            @click="handleNavClick('faq_6')"
          />
          <ListItemNav
            :name="$t('wiki_faq_7')"
            @click="handleNavClick('faq_7')"
          />
          <ListItemNav
            :name="$t('wiki_faq_8')"
            @click="handleNavClick('faq_8')"
          />
          <ListItemNav
            :name="$t('wiki_faq_9')"
            @click="handleNavClick('faq_9')"
          />
          <ListItemNav
            :name="$t('wiki_faq_10')"
            @click="handleNavClick('faq_10')"
          />
          <ListItemNav
            :name="$t('wiki_faq_11')"
            @click="handleNavClick('faq_11')"
          />
          <ListItemNav
            :name="$t('wiki_faq_12')"
            @click="handleNavClick('faq_12')"
          />
          <ListItemNav
            :name="$t('wiki_faq_13')"
            @click="handleNavClick('faq_13')"
            :divider="false"
          />
        </ListContainer>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { goBack } from '../util/mixins';

export default {
  name: 'ProductWiki',
  mixins: [goBack],
  data() {
    return {
      dialogList: [] // 没有弹窗，空数组
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight'])
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
    handleNavClick(type) {
      switch(type) {
        case 'quick_start':
          this.$router.push({ name: 'WikiQuickStart' });
          break;
        case 'usage_tips':
          this.$router.push({ name: 'WikiUsageTips' });
          break;
        case 'air_quality':
          this.$router.push({ name: 'WikiAirQuality' });
          break;
        case 'troubleshooting':
          this.$router.push({ name: 'WikiTroubleshooting' });
          break;
        case 'faq_1':
        case 'faq_2':
        case 'faq_3':
        case 'faq_4':
        case 'faq_5':
        case 'faq_6':
        case 'faq_7':
        case 'faq_8':
        case 'faq_9':
        case 'faq_10':
        case 'faq_11':
        case 'faq_12':
        case 'faq_13':
          this.$router.push({ name: 'WikiFAQ', query: { faq: type } });
          break;
        default:
          break;
      }
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/ProductWiki.less");
</style>
