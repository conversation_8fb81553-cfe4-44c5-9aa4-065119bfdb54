<template>
  <div class="section-mode" :class="{'disabled' : (disabled && !inside), 'inside' : inside}">
    <div class="mode-box">
      <div class="mode-item" v-for="(item, index) in modes" :key="index" :id="'com.huawei.smarthome:id/'+ (item + index)" @click="switchMode(item, index)">
        <div class="icon-box" :class="{'active': mode == item.value}">
          <img class="img" :src="modeIcon(item)">
        </div>
        <div class="text-box">
          <span class="text" :class="{'active': mode == item.value}">{{ item.name }}</span>
          <img class="arrow" :src="arrowImg(item)" v-if="item.spinner"
               :class="{'arotate': isShowSpinnerPicker && selectSpinnerMode === item.value}">
        </div>
      </div>
      <div class="divider" v-if="divider"></div>
    </div>
    <Spinner ref="spinner" :values="spinnerValues" :value="spinnerValue" :show="isShowSpinnerPicker"
             @cancel="isShowSpinnerPicker = false"
             @confirm="onSpinnerConfirm"></Spinner>
    <slot></slot>
  </div>
</template>

<script>
import Spinner from './Spinner';

export default {
  name: 'ModeBar',
  components: {Spinner},
  props: {
    mode: {
      default: -1,
      type: Number
    },
    modes: {
      default: Array(),
      type: Array
    },
    disabled: {
      default: true,
      type: Boolean
    },
    inside: {
      type: Boolean,
      default: false
    },
    divider: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      lastMode: null,
      spinnerValue: -1, //spinner当前值
      spinnerValues: [], //选项值列表
      isShowSpinnerPicker: false, //是否显示Spinner
      selectSpinnerMode: -1, //选中的是哪个Spinner
      selectModeIndex: -1
    };
  },
  watch: {
    selectModeIndex() {
      let el = this.$refs.spinner.$el.getElementsByClassName('spinnerWrap')[0];
      el.style.width = '40%';
      el.style.bottom = '10rem';
      if (this.selectModeIndex === this.modes.length - 1) {
        el.style.left = '60%';
      } else {
        el.style.left = ((100 / this.modes.length) * this.selectModeIndex) + '%';
      }
    }
  },
  methods: {
    modeIcon(mode) {
      if (this.mode === mode.value) {
        return require(`@/assets/${mode.img}_on.png`);
      } else {
        return require(`@/assets/${this.$store.getters.imgPath}${mode.img}_off.png`);
      }
    },
    switchMode(item, index) {
      this.selectModeIndex = index;
      if (this.disabled) {
        return;
      }
      if (item.spinner) {
        this.selectSpinnerMode = item.value;
        this.spinnerValues = item.spinner.items;
        this.spinnerValue = item.spinner.value;
        this.isShowSpinnerPicker = true;
        return;
      }
      if (this.mode === item.value) {
        return;
      }
      this.$emit('switchMode', item);
    },
    onSpinnerConfirm(item) {
      this.isShowSpinnerPicker = false;
      this.$emit('spinnerClicked', this.selectSpinnerMode, item);
    },
    arrowImg(item) {
      if (this.mode === item.value) {
        return require(`../assets/${this.$store.getters.imgPath}ic_spinner_selected_down.png`);
      } else {
        return require(`../assets/${this.$store.getters.imgPath}ic_spinner_normal_down.png`);
      }
    }
  }
};
</script>

<style lang='less' scoped>
@import url("../style/public.less");

.section-mode {
  width: 100%;
  .cardStyle();
  position: relative;
  padding: 0px 0.8rem;

  &.disabled {
    opacity: .4;
  }

  &.inside {
    margin: 0px;
    padding: 0px;
  }

  .mode-box {
    position: relative;
    color: var(--emui_text_primary);
    height: 8.8rem;
    display: flex;
    box-sizing: border-box;
    padding: 1.6rem 0px 0px;
    justify-content: space-around;

    .divider {
      position: absolute;
      width: calc(100% - 1.6rem);
      height: 1px;
      bottom: 0;
      left: 0.8rem;
      transform: scaleY(0.25);
      background: var(--emui_color_divider_horizontal);
    }

    .mode-item {
      position: relative;
      display: flex;
      flex-flow: column;
      align-items: center;

      .icon-box {
        width: 4.0rem;
        height: 4.0rem;
        text-align: center;
        background-color: var(--mode-icon-unselect-bg);
        border-radius: 2.0rem;
        display: flex;
        justify-content: center;
        align-items: center;

        &.active {
          background-color: var(--emui_accent);
        }

        .img {
          width: 2.4rem;
          height: 2.4rem;
          border: none;
        }
      }

      .text-box {
        line-height: 2.4rem;
        height: 2.4rem;
        display: flex;
        align-items: center;

        .text {
          color: var(--emui_text_secondary);
          font-size: 1.2rem;
          white-space: nowrap;

          &.active {
            color: var(--emui_accent);
          }
        }

        .arrow {
          height: 2.4rem;
          width: 1.2rem;
          margin-left: 0.2rem;
          transition: transform 0.25s;
        }

        .arotate {
          transform: rotate(180deg);
        }
      }

    }
  }
}

.pad #home .section-mode {
  padding: 0px;
}

.tahiti #home .section-mode {
  padding: 0px;
}

</style>
