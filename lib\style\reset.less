/*禁止页面复制*/
* {
    -webkit-touch-callout: none;
    /*系统默认菜单被禁用*/
    -webkit-user-select: none;
    user-select: none;
}
* {
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: transparent;
    line-height: 1.3;
}
html {
    font-family:'<PERSON>o','<PERSON><PERSON>','Source Han Sans','PingFangSC-Regular';
    -webkit-text-size-adjust: 100%;
}
html,
body,
#app {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
li {
    list-style: none;
}
a {
    text-decoration: none
}
button {
    outline: none;
    border: none;
    background-color: transparent;
}

img {
  outline: none;
  border: none;
}
