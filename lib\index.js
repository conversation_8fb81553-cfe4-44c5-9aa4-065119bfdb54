import Vue from 'vue';

import './style/reset.less';
import './style/variables.less';
import Toast from './components/Toast/Toast.js';
import MessageBox from "./components/message-box/message-box.js"

// 导入单个组件
import DeviceShow from "./components/DeviceShow"
import BleStatusBar from "./components/BleStatusBar"
import ControlBar from "./components/ControlBar"
import GuidePage from "./components/GuidePage"
import DialogPicker from "./components/DialogPicker"
import ModeBar from "./components/ModeBar"
import Spinner from "./components/Spinner"
import Picker from './components/picker/picker';
import Range from './components/range/index';
import Alert from './components/Alert';
import LRControlBar from './components/LRControlBar';
import SliderBar from './components/SliderBar';
import Container from './components/Container';
import Titlebar from './components/Titlebar';
import InfoBar from './components/InfoBar';
import SwitchBar from './components/SwitchBar';
import SubTitle from './components/SubTitle';
import DialogTimePicker from './components/DialogTimePicker';
import DialogSetName from './components/DialogSetName';
import DialogRadioPicker from './components/DialogRadioPicker';
import DialogWeekPicker from './components/DialogWeekPicker';
import DialogDatePicker from './components/DialogDatePicker';

import ListContainer from './components/ListContainer';
import ListItemSwitch from './components/list-item/list-item-switch'
import ListItemRadio from './components/list-item/list-item-radio'
import ListItemPicker from './components/list-item/list-item-picker'
import ListItemTime from './components/list-item/list-item-time'
import ListItemNav from './components/list-item/list-item-nav'

Vue.prototype.$toast = Toast;
Vue.prototype.$messagebox = MessageBox;

// 以数组的结构保存组件，便于遍历
const components = [
  DeviceShow,
  BleStatusBar,
  ControlBar,
  GuidePage,
  DialogPicker,
  ModeBar,
  Spinner,
  Picker,
  Range,
  Alert,
  LRControlBar,
  SliderBar,
  Container,
  Titlebar,
  InfoBar,
  SwitchBar,
  SubTitle,
  DialogTimePicker,
  DialogSetName,
  DialogRadioPicker,
  DialogWeekPicker,
  DialogDatePicker,

  ListContainer,
  ListItemSwitch,
  ListItemRadio,
  ListItemPicker,
  ListItemTime,
  ListItemNav
]

// 定义 install 方法
const install = function (Vue) {
  if (install.installed) return
  install.installed = true
  // 遍历并注册全局组件
  components.map(component => {
    Vue.component(component.name, component)
  })
  // 挂载MessageBox到Vue原型上
  Vue.prototype.$messagebox = MessageBox
}

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default {
  // 导出的对象必须具备一个 install 方法
  install,
  MessageBox
}

