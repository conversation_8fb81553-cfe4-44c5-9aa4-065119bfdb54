# SubHome重构任务

## 任务背景
用户希望在SubHome中实现室内环境质量的tab栏，根据提供的截图显示"日报"、"周报"、"月报"三个选项卡，并集成EnvironmentOverview组件。

## 执行计划
1. 重构SubHome.vue组件
2. 添加EnvironmentOverview组件到页面顶部
3. 实现自定义tab栏样式，匹配截图设计
4. 添加tab内容区域用于显示不同时间段数据
5. 更新样式文件和国际化文本

## 已完成的工作

### 1. 重构SubHome.vue组件
- ✅ 移除了原有的Element UI tabs实现
- ✅ 添加了EnvironmentOverview组件到页面顶部
- ✅ 实现了自定义tab栏，包含"日报"、"周报"、"月报"三个选项
- ✅ 添加了tab切换逻辑和内容区域
- ✅ 集成了环境数据的状态管理

### 2. 样式实现
- ✅ 更新了SubHome.less样式文件
- ✅ 实现了自定义tab栏样式，包括：
  - 选中状态的蓝色下划线效果
  - hover状态的颜色变化
  - 响应式布局支持
  - 暗黑模式兼容性

### 3. 国际化文本
- ✅ 在zh.json中添加了占位符文本
- ✅ 在en.json中添加了对应的英文文本

### 4. 组件集成
- ✅ 导入并使用了EnvironmentOverview组件
- ✅ 传递了必要的环境数据props
- ✅ 添加了goBack方法和tab切换处理

## 技术实现细节

### 组件结构
```
SubHome.vue
├── Titlebar (标题栏)
├── environment-section (环境质量概览)
│   └── EnvironmentOverview
└── custom-tabs (自定义Tab栏)
    ├── tab-header (Tab标题)
    └── tab-content (Tab内容)
```

### 样式特点
- 使用CSS变量确保主题兼容性
- 实现了选中状态的蓝色下划线动画
- 支持暗黑模式和响应式布局
- 遵循现有的设计规范和间距标准

### 数据流
- 从Vuex状态管理获取环境数据
- 支持tab切换和内容动态显示
- 预留了历史数据加载的扩展点

## 下一步计划
1. 添加实际的报表数据展示组件
2. 实现历史数据查询API集成
3. 添加图表展示功能
4. 优化加载状态和错误处理

## 当前状态
✅ 基础重构完成，页面结构和样式已实现
⏳ 等待用户测试和反馈
