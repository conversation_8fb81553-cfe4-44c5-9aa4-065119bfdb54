<template>
  <div class="tk-range">
    <div class="mt-range" :class="{ 'mt-range-disabled': disabled }">
      <slot name="start"></slot>
      <div class="mt-range-content" ref="content">
        <div class="mt-range-runway" ref="runway"></div>
        <div class="mt-range-xpoints" v-show="showXAxis">
          <span v-for="n in xAxisCount"></span>
        </div>
        <div class="mt-range-progress" :style="{ width: progressWidth + 'px'}"></div>
        <div :id="id" class="mt-range-thumb" ref="thumb" :style="{ left: progress + 'px' }"></div>
      </div>
      <slot name="end"></slot>
    </div>
    <div class="x-axis" v-show="showXAxis" ref="xAxis">
      <span v-for="n in xAxisCount" class="text">{{ min + (n - 1) * step }}</span>
    </div>
  </div>

</template>

<style lang="less">
.tk-range {
  display: flex;
  flex-flow: column;
  position: relative;

  .x-axis {
    position: relative;
    margin-top: 0.2rem;
    display: flex;
    margin-right: -1.6rem;

    >span {
      position: absolute;
      top: 0px;
      font-size: 1rem;
      color: var(--emui_text_secondary);

      &:first-child {
        position: relative;
        left: 0px;
        text-align: left;
      }

      &:last-child {
        right: 0px;
        text-align: right;
      }
    }
  }
}

.mt-range {
  position: relative;
  display: flex;
  height: 1.6rem;
  line-height: 1.6rem;

  >* {
    display: flex;
    display: -webkit-box;
  }

  *[slot=start] {
    margin-right: 0.4rem;
  }

  *[slot=end] {
    margin-left: 0.4rem;
  }

  .mt-range-content {
    position: relative;
    flex: 1;
  }

  .mt-range-runway {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: -1.6rem;
    height: 0.4rem;
    background-color: var(--emui_control_normal);
    border-radius: 0.2rem;
  }

  .mt-range-xpoints {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    height: 0.4rem;
    display: flex;
    justify-content: space-between;
    right: -1.6rem;

    >span {
      display: inline-block;
      height: 0.4rem;
      width: 0.4rem;
      background-color: var(--emui_color_divider_horizontal);

      &:last-child {
        background-color: transparent;
      }
    }

  }

  .mt-range-thumb {
    background-color: #FFFFFF;
    position: absolute;
    left: 0;
    top: 0px;
    width: 1.6rem;
    height: 1.6rem;
    border-radius: 100%;
    cursor: move;
    box-shadow: 0px 1px 2px 1px var(--color_thumb_shadow), 0px 0px 1px 1px var(--color_thumb_shadow);
    opacity: 1;
    border: 1px solid var(--color_thumb_border);

    &::after {
      content: "";
      position: absolute;
      top: -2rem;
      left: -2rem;
      right: -2rem;
      bottom: -2rem;
    }
  }

  .mt-range-progress {
    position: absolute;
    display: block;
    height: 0.4rem;
    border-radius: 0.2rem;
    background-color: var(--emui_slide_progress_bg);
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: 0;
  }

  .mt-range-disabled {
    opacity: 0.5;
  }
}
</style>

<script type="text/babel">
import draggable from './draggable';

export default {
  name: 'range',

  props: {
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 100
    },
    step: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Number
    },
    showXAxis: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      xAxisCount: 1,
      runwayWidth: 0,
      thumbWidth: 0
    };
  },
  computed: {
    progress() {
      const value = this.value;
      if (typeof value === 'undefined' || value === null) return 0;
      let thumbWidth = 0;
      if (this.value == this.max) {
        thumbWidth = this.thumbWidth;
      } else if (this.value == this.min) {
        thumbWidth = 0;
      } else {
        thumbWidth = this.thumbWidth / 2;
      }
      return (value - this.min) / (this.max - this.min) * this.runwayWidth - thumbWidth;
    },
    progressWidth() {
      const value = this.value;
      if (typeof value === 'undefined' || value === null) return 0;
      return (value - this.min) / (this.max - this.min) * this.runwayWidth;
    }
  },

  mounted() {
    this.xAxisCount = (this.max - this.min) / this.step + 1;
    const thumb = this.$refs.thumb;
    const content = this.$refs.content;
    setTimeout(() => {
      this.$nextTick(() => {
        this.runwayWidth = this.$refs.runway.getBoundingClientRect().width;
        this.thumbWidth = thumb.getBoundingClientRect().width;
        let xAxisWidth = this.$refs.xAxis.getBoundingClientRect();
        let itemWidth = xAxisWidth.width / (this.xAxisCount - 1);
        let xAxisElements = this.$refs.xAxis.getElementsByClassName('text');
        for (var i = 1; i < xAxisElements.length - 1; i++) {
          let element = xAxisElements[i];
          let marginLeft = itemWidth * i - element.offsetWidth / 2;
          element.style.left = marginLeft + 'px';
        }
      });
    }, 100)

    const getThumbPosition = () => {
      const contentBox = content.getBoundingClientRect();
      const thumbBox = thumb.getBoundingClientRect();
      return {
        left: thumbBox.left - contentBox.left,
        top: thumbBox.top - contentBox.top,
        thumbBoxLeft: thumbBox.left
      };
    };

    let dragState = {};
    draggable(thumb, {
      start: (event) => {
        if (this.disabled) return;
        const position = getThumbPosition();
        const thumbClickDetalX = event.clientX - position.thumbBoxLeft;
        dragState = {
          thumbStartLeft: position.left,
          thumbStartTop: position.top,
          thumbClickDetalX: thumbClickDetalX
        };
      },
      drag: (event) => {
        if (this.disabled) return;
        const contentBox = content.getBoundingClientRect();
        const deltaX = event.pageX - contentBox.left - dragState.thumbStartLeft - dragState.thumbClickDetalX;
        const stepCount = Math.ceil((this.max - this.min) / this.step);
        const newPosition = (dragState.thumbStartLeft + deltaX) - (dragState.thumbStartLeft + deltaX) % (contentBox.width / stepCount);

        let newProgress = newPosition / contentBox.width;

        if (newProgress < 0) {
          newProgress = 0;
        } else if (newProgress > 1) {
          newProgress = 1;
        }

        this.$emit('input', Math.round(this.min + newProgress * (this.max - this.min)));
      },
      end: () => {
        if (this.disabled) return;
        this.$emit('change', this.value);
        dragState = {};
      }
    });
  }
};
</script>
