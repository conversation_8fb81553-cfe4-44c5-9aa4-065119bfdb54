<template>
    <div class="mod-subtitle">
        <span class="text">{{ title }}</span>
    </div>
</template>

<script>
export default {
    name: "SubTitle",
    props: {
        title: {
            default: null,
            type: String
        }
    }
}
</script>

<style lang='less' scoped>
.mod-subtitle {
    width: 100%;
    margin: 1.2rem var(--emui_dimens_card_start) 0.8rem var(--emui_dimens_card_end);
    padding: 0 1.2rem;
    font-size: 1.6rem;
    color: var(--emui_text_primary);

    .text {
        display: inline-block;
        height: 2.4rem;
        line-height: 2.4rem;
        text-align: center;
    }
}
</style>
