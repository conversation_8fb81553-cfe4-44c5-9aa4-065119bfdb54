<template>
  <div id="wiki-quick-start-page">
    <Titlebar
      :title="$t('wiki_quick_start')"
      :showRightIcon="false"
      @leftClick="$router.goBack()"
    />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      
      <div class="wiki-content">
        <div class="section">
          
          <div class="subsection">
            <div class="subsection-title">(1)开关机</div>
            <div class="subsection-content">
              <div class="content-item">
                <div class="item-title">开机</div>
                <div class="item-text">1）关机状态且未接入电源时：接入外部电源。</div>
                <div class="item-text">2）关机状态且已接入外部电源时：长按按键≥6S。</div>
              </div>
              <div class="content-item">
                <div class="item-title">关机</div>
                <div class="item-text">设备处于开机状态时：长按≥6S 关机。</div>
              </div>
            </div>
          </div>

          <div class="subsection">
            <div class="subsection-title">(2)重置网络</div>
            <div class="subsection-content">
              <div class="item-text">设备处于开机状态，长按按键3~5秒，设备进入配网模式/重置网络，按照APP提示，为设备配置WiFi账号及密码（仅支持2.4GHz Wi-Fi）。</div>
            </div>
          </div>

          <div class="subsection">
            <div class="subsection-title">(3)智能熄屏设置</div>
            <div class="subsection-content">
              <div class="item-text">1）打开智慧生活APP，进入"四合一空气质量传感器"页面，点击图标，设置熄屏时间，在非睡眠模式时间段内，感应无人靠近后，设备将自动熄屏。</div>
              <div class="item-text">2）如需关闭此功能，则点击即可，设备将在非睡眠模式时间段内持续亮屏。</div>
              <div class="item-text">温馨提示：设备电源已切换到电池消耗状态，此状态下将关闭并禁用"智能熄屏及睡眠模式"功能（默认设为30秒自动熄屏），同时APP将弹出以下提醒：</div>
              <div class="image-container">
                <img :src="require('../assets/quick_1.png')" alt="电池提醒" class="content-image" />
              </div>
            </div>
          </div>

          <div class="subsection">
            <div class="subsection-title">(4)睡眠模式设置</div>
            <div class="subsection-content">
              <div class="item-text">1）启用睡眠模式：打开智慧生活APP，进入"四合一空气质量传感器"页面，点击图标，启用睡眠模式功能并设置开启、关闭时间、重复周期等。</div>
              <div class="item-text">2）启用告警灯显示：启用睡眠模式下，可选启用告警灯显示，在睡眠模式下，当二氧化碳、甲醛等数值超标时，设备将显示告警灯：</div>
              <div class="image-container">
                <img :src="require('../assets/quick_2.png')" alt="告警灯" class="content-image" />
              </div>
              <div class="item-text">3）关闭睡眠模式：如需关闭此功能，则点击即可，设备将不区分时间段仅跟随"智能熄屏"模式设置亮屏或熄屏。</div>
            </div>
          </div>

          <div class="subsection">
            <div class="subsection-title">(5)场景联动</div>
            <div class="subsection-content">
              <div class="item-text">当设备检测到温度、湿度、二氧化碳、甲醛超过某个范围时，可联动打开第三方设备（如：空调、加湿器、除湿机、净化器、推窗器等），实现智能化场景联动。操作方式：</div>
              <div class="item-text">1）打开智慧生活APP→场景→＋→添加条件，可以将设备的状态作为触发条件：</div>
              <div class="image-container">
                <img :src="require('../assets/quick_3.png')" alt="场景联动" class="content-image" />
              </div>
              <div class="item-text">2）选择设备对应的触发条件之后，选择创建场景→执行任务，可选"系统功能""控制场景""智能设备"等执行任务，如需联动第三方设备（空调、加湿器、除湿机、净化器、推窗器等），则选"智能设备"及其对应的执行动作后，点击右上角"√"保存完成设置。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { goBack } from '../util/mixins';

export default {
  name: 'WikiQuickStart',
  mixins: [goBack],
  data() {
    return {
      dialogList: [] // 没有弹窗，空数组
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight'])
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
  }
};
</script>

<style lang="less" scoped>
@import url("../../lib/style/public.less");

#wiki-quick-start-page {
  overflow-y: auto;
  background-color: var(--emui_color_subbg);

  .content {
    margin: 6.6rem 2.4rem 0px 2.4rem;
    position: relative;

    .wiki-content {

      .section {
        margin-bottom: 2rem;

        .section-title {
          font-size: 1.8rem;
          font-weight: 600;
          color: var(--emui_text_primary);
          margin-bottom: 1.6rem;
          line-height: 1.4;
        }
        
        .subsection {
          margin-bottom: 2rem;
          
          .subsection-title {
            font-size: 1.6rem;
            font-weight: 500;
            color: var(--emui_text_primary);
            margin-bottom: 1.2rem;
            line-height: 1.4;
          }
          
          .subsection-content {
            .content-item {
              margin-bottom: 1.6rem;
              
              .item-title {
                font-size: 1.4rem;
                font-weight: 500;
                color: var(--emui_text_primary);
                margin-bottom: 0.8rem;
                line-height: 1.4;
              }
              
              .item-text {
                font-size: 1.4rem;
                color: var(--emui_text_secondary);
                line-height: 1.6;
                margin-bottom: 0.8rem;
              }
            }
            
            .item-text {
              font-size: 1.4rem;
              color: var(--emui_text_secondary);
              line-height: 1.6;
              margin-bottom: 1.2rem;
            }
            
            .image-container {
              text-align: center;
              margin: 1.6rem 0;

              .content-image {
                max-width: 80%;
                height: auto;
              }
            }
          }
        }
      }
    }
  }
}

// 决策理由：适配平板设备的边距
.pad #wiki-quick-start-page {
  .content {
    margin: 6.6rem var(--home_margin) 0px var(--home_margin);
  }
}
</style>
