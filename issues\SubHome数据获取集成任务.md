# SubHome数据获取集成任务

## 任务背景
用户需要在 SubHome 组件中集成 `getDeviceStatistics` 方法，为4个不同传感器类型（CO2、温度、湿度、HCHO）实现统计数据获取，并将数据传递给 LineChart 组件进行渲染。

## 执行计划
1. ✅ 修改 SubHome.vue 组件，添加数据获取逻辑
2. ✅ 修改 LineChart.vue 组件，支持外部数据驱动
3. ✅ 集成数据传递，连接数据获取和图表渲染
4. ⏳ 测试验证功能

## 已完成的工作

### 1. SubHome.vue 组件修改
- ✅ 在 `data()` 中添加了图表数据存储和传感器配置
- ✅ 添加了传感器类型映射配置：
  ```javascript
  sensorConfig: {
      2: { sid: 'co2', character: 'current', dataType: 'co2' },
      3: { sid: 'hcho', character: 'current', dataType: 'hcho' },
      4: { sid: 'temperature', character: 'current', dataType: 'temperature' },
      5: { sid: 'humidity', character: 'current', dataType: 'humidity' }
  }
  ```
- ✅ 实现了统一的 `fetchDeviceStatistics()` 方法
- ✅ 添加了 `getTimeRange()` 方法计算时间范围
- ✅ 添加了 `updateChartWithData()` 方法处理数据更新
- ✅ 在 `activated()` 和 `toggle()` 方法中调用数据获取
- ✅ 修改了 `refreshData()` 方法触发数据获取

### 2. LineChart.vue 组件修改
- ✅ 添加了 `chartDataFromParent` prop 接收外部数据
- ✅ 修改了 `chartData` computed 属性，优先使用外部数据
- ✅ 添加了 watch 监听外部数据变化，自动重新渲染图表
- ✅ 保留静态数据作为 fallback

### 3. 数据传递集成
- ✅ 为所有4个传感器类型的 LineChart 组件添加了数据绑定：
  - CO2: `:chartDataFromParent="chartData.co2"`
  - HCHO: `:chartDataFromParent="chartData.hcho"`
  - 温度: `:chartDataFromParent="chartData.temperature"`
  - 湿度: `:chartDataFromParent="chartData.humidity"`

## 技术实现细节

### 时间范围计算
- **日报**: 选中日期的 00:00 到 23:59，精度为 HOUR
- **周报**: 选中周的周一到周日，精度为 DAILY  
- **月报**: 选中月份的第一天到最后一天，精度为 DAILY

### 数据流程
1. 用户选择传感器类型和报表类型
2. `fetchDeviceStatistics()` 根据配置调用 API
3. `updateChartWithData()` 处理返回数据
4. 数据存储到 `chartData` 对象中
5. LineChart 组件通过 props 接收数据并渲染

### 错误处理
- 添加了加载状态管理 (`isLoadingData`)
- API 调用失败时回退到静态数据
- 无效数据的处理和日志记录

## 🔄 接口切换更新 (2024-06-19)

### 接口变更
- ✅ 从 `getDeviceStatistics` 切换到 `getDevHistory` 接口
- ✅ 适配UTC时间格式和分页参数
- ✅ 处理历史数据的list数组格式

### 新增功能
- ✅ 添加了 `getDevHistory` action 到 store/actions.js
- ✅ 实现了UTC时间格式化方法 `formatToUTC()`
- ✅ 添加了历史数据处理方法 `processHistoryData()`
- ✅ 实现了数据聚合算法：
  - 日报：4个区间（每6小时）
  - 周报：7天数据
  - 月报：6-7个区间（每4-5天）

### X轴标签更新
- ✅ 日报：['00:00', '06:00', '12:00', '18:00']
- ✅ 周报：['03/06', '03/07', '03/08', '03/09', '03/10', '03/11', '03/12']
- ✅ 月报：['03/01', '03/05', '03/10', '03/15', '03/20', '03/25', '03/31']

### 时间范围计算
- ✅ 日报：最近24小时（从当前整点往前推）
- ✅ 周报：最近7天
- ✅ 月报：当月第一天到最后一天

## 待测试项目
- [ ] getDevHistory 接口调用是否正常
- [ ] UTC时间格式转换是否正确
- [ ] 历史数据解析和聚合是否准确
- [ ] 不同报表类型的数据聚合逻辑是否正确
- [ ] X轴标签显示是否符合UI设计
- [ ] 错误处理和 fallback 机制是否有效
- [ ] 日期切换时数据刷新是否正常

## 注意事项
- 历史数据接口返回的数据结构为 `{totalCount, pageNo, pageSize, list}`
- list 中每个元素包含 `{devId, gatewayId, sid, data, timestamp}`
- 需要根据实际API响应调整 `extractValueFromData()` 方法中的字段映射
- UTC时间格式为 `yyyyMMddTHHmmssZ`，如：`20151212T121212Z`
- 数据聚合时会过滤掉无效数据，空区间返回 null
