<template>
    <div class="dialog" @click="cancel(false)">
        <div class="dialog-container" @click.stop>

            <!-- 自定义日历头部导航 -->
            <div class="calendar-header">
                <div class="month-nav">
                    <img class="nav-btn" @click.stop="navigateMonth('prev')" :src="require(`../../src/assets/${$store.state.isDarkMode ? 'dark/' : ''}ic_left_arrow.png`)">
                    </img>
                    <span class="month-title">{{
                        getCurrentMonthTitle()
                    }}</span>
                    <img class="nav-btn" @click.stop="navigateMonth('next')" :src="require(`../../src/assets/${$store.state.isDarkMode ? 'dark/' : ''}ic_right_arrow.png`)">

                    </img>
                </div>
                <span class="today" @click.stop="goToToday">{{ mode === 'yearly' ? '本月' : $t("today") }}</span>
            </div>

            <!-- 日历主体 -->
            <div class="calendar-content" :class="{ 'yearly-mode': mode === 'yearly' }" @click.stop>
                <!-- 日报模式：单日选择 -->
                <CustomCalendar
                    v-if="mode === 'daily'"
                    ref="dailyCalendar"
                    mode="single"
                    :current-date="currentDisplayDate"
                    :selected-date="tempSelectedDate"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @date-select="onDateSelect"
                />

                <!-- 周报模式：单日选择 -->
                <CustomCalendar
                    v-if="mode === 'weekly'"
                    ref="weeklyCalendar"
                    mode="single"
                    :current-date="currentDisplayDate"
                    :selected-date="tempSelectedWeekDate"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @date-select="onWeekDateSelect"
                />

                <!-- 月报模式：月选择 -->
                <CustomCalendar
                    v-if="mode === 'monthly'"
                    ref="monthlyCalendar"
                    mode="single"
                    :current-date="currentDisplayDate"
                    :selected-date="tempSelectedMonth"
                    :min-date="minDate"
                    :max-date="maxDate"
                    @date-select="onMonthSelect"
                />

                <!-- 年报模式：年月选择 -->
                <YearlyCalendar
                    v-if="mode === 'yearly'"
                    ref="yearlyCalendar"
                    :current-date="currentDisplayDate"
                    :selected-date="tempSelectedYear"
                    :min-date="minDate"
                    :max-date="maxDate"
                    :data-availability="yearlyDataAvailability"
                    @year-change="onYearChange"
                    @month-select="onYearlyMonthSelect"
                />
            </div>

            <!-- 底部按钮 -->
            <div class="dialog-btns">
                <div @click.stop="cancel(true)">
                    <p :class="{ red: leftBtnRed }">
                        {{ leftBtnText || $t("cancel") }}
                    </p>
                </div>
                <span class="line"></span>
                <div @click.stop="confirm">
                    <p>{{ $t("ok") }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomCalendar from './CustomCalendar.vue';
import YearlyCalendar from './YearlyCalendar.vue';

export default {
    name: "DialogDatePicker",
    components: {
        CustomCalendar,
        YearlyCalendar
    },
    props: {
        title: {
            type: String,
            default: "",
        },
        subtitle: {
            type: String,
            default: null,
        },
        mode: {
            type: String,
            default: "daily", // 'daily', 'weekly', 'monthly', 'yearly'
            validator: (value) =>
                ["daily", "weekly", "monthly", "yearly"].includes(value),
        },
        defaultValue: {
            type: [Date, Array],
            default: () => new Date(),
        },
        maxDate: {
            type: Date,
            default: () => new Date(),
        },
        minDate: {
            type: Date,
            default: () => new Date(2020, 0, 1),
        },
        leftBtnText: {
            type: String,
            default: null,
        },
        leftBtnRed: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            tempSelectedDate: new Date(),
            tempSelectedWeek: [], // 保留用于兼容性，但不再直接使用
            tempSelectedWeekDate: new Date(), // 周报模式选中的具体日期
            tempSelectedMonth: new Date(),
            tempSelectedYear: new Date(), // 年报模式选中的年月
            // 当前显示的月份（独立于选中的日期）
            currentDisplayDate: new Date(),
            // 年报数据可用性
            yearlyDataAvailability: [],
        };
    },
    watch: {
        tempSelectedDate() {
            this.$nextTick(() => {
                this.forceCalendarStyles();
            });
        },
    },
    mounted() {
        this.initializeValues();
        this.$nextTick(() => {
            this.forceCalendarStyles();
        });
    },
    methods: {
        initializeValues() {
            const today = new Date();

            if (this.mode === "daily") {
                this.tempSelectedDate =
                    this.defaultValue instanceof Date
                        ? new Date(this.defaultValue)
                        : new Date(today);
                // 初始化显示月份为选中日期的月份，使用该月的第一天
                this.currentDisplayDate = new Date(this.tempSelectedDate.getFullYear(), this.tempSelectedDate.getMonth(), 1);
            } else if (this.mode === "weekly") {
                if (
                    Array.isArray(this.defaultValue) &&
                    this.defaultValue.length === 2
                ) {
                    // 如果传入的是周范围，使用周的开始日期作为选中日期
                    this.tempSelectedWeekDate = new Date(this.defaultValue[0]);
                    this.tempSelectedWeek = [
                        new Date(this.defaultValue[0]),
                        new Date(this.defaultValue[1]),
                    ];
                    // 初始化显示月份为选中日期的月份
                    this.currentDisplayDate = new Date(this.tempSelectedWeekDate.getFullYear(), this.tempSelectedWeekDate.getMonth(), 1);
                } else if (this.defaultValue instanceof Date) {
                    // 如果传入的是单个日期，直接使用该日期
                    this.tempSelectedWeekDate = new Date(this.defaultValue);
                    const startOfWeek = this.getStartOfWeek(this.tempSelectedWeekDate);
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 6);
                    this.tempSelectedWeek = [startOfWeek, endOfWeek];
                    this.currentDisplayDate = new Date(this.tempSelectedWeekDate.getFullYear(), this.tempSelectedWeekDate.getMonth(), 1);
                } else {
                    // 默认使用今天
                    this.tempSelectedWeekDate = new Date(today);
                    const startOfWeek = this.getStartOfWeek(today);
                    const endOfWeek = new Date(startOfWeek);
                    endOfWeek.setDate(startOfWeek.getDate() + 6);
                    this.tempSelectedWeek = [startOfWeek, endOfWeek];
                    this.currentDisplayDate = new Date(today.getFullYear(), today.getMonth(), 1);
                }
            } else if (this.mode === "monthly") {
                this.tempSelectedMonth =
                    this.defaultValue instanceof Date
                        ? new Date(this.defaultValue)
                        : new Date(today);
                this.currentDisplayDate = new Date(this.tempSelectedMonth.getFullYear(), this.tempSelectedMonth.getMonth(), 1);
            } else if (this.mode === "yearly") {
                this.tempSelectedYear =
                    this.defaultValue instanceof Date
                        ? new Date(this.defaultValue)
                        : new Date(today);
                // 年报模式显示选中年份
                this.currentDisplayDate = new Date(this.tempSelectedYear.getFullYear(), 0, 1);
                // 获取年度数据可用性
                this.fetchYearlyDataAvailability();
            }
        },
        getStartOfWeek(date) {
            const d = new Date(date);
            const day = d.getDay();
            const diff = d.getDate() - day + (day === 0 ? -6 : 1);
            return new Date(d.setDate(diff));
        },
        getMonthStart(date) {
            const d = new Date(date);
            return new Date(d.getFullYear(), d.getMonth(), 1);
        },
        getMonthEnd(date) {
            const d = new Date(date);
            return new Date(d.getFullYear(), d.getMonth() + 1, 0);
        },
        navigateMonth(direction) {
            if (this.mode === 'yearly') {
                // 年报模式：进行年份导航
                this.navigateYear(direction);
                return;
            }

            // 基于当前显示月份进行导航，使用安全的日期设置避免溢出
            const currentYear = this.currentDisplayDate.getFullYear();
            const currentMonth = this.currentDisplayDate.getMonth();

            let newYear = currentYear;
            let newMonth = currentMonth;

            if (direction === "prev") {
                newMonth = currentMonth - 1;
                if (newMonth < 0) {
                    newMonth = 11;
                    newYear = currentYear - 1;
                }
            } else {
                newMonth = currentMonth + 1;
                if (newMonth > 11) {
                    newMonth = 0;
                    newYear = currentYear + 1;
                }
            }

            // 使用月份的第一天来避免日期溢出问题
            this.currentDisplayDate = new Date(newYear, newMonth, 1);

            // 检查选中的日期是否在新的显示月份中，如果不在则清除选中状态
            if (this.mode === "daily") {
                const selectedMonth = this.tempSelectedDate.getMonth();
                const selectedYear = this.tempSelectedDate.getFullYear();
                const displayMonth = this.currentDisplayDate.getMonth();
                const displayYear = this.currentDisplayDate.getFullYear();

                // 如果选中的日期不在当前显示的月份中，清除选中状态
                if (selectedMonth !== displayMonth || selectedYear !== displayYear) {
                    // 可以选择清除选中状态，或者保持选中状态但不显示
                    // 这里我们保持选中状态，但日历会正确显示当前月份
                }
            } else if (this.mode === "weekly") {
                // 周报模式：检查选中的周是否与当前显示月份有交集
                if (this.tempSelectedWeek.length > 0) {
                    const weekStartMonth = this.tempSelectedWeek[0].getMonth();
                    const weekStartYear = this.tempSelectedWeek[0].getFullYear();
                    const weekEndMonth = this.tempSelectedWeek[1].getMonth();
                    const weekEndYear = this.tempSelectedWeek[1].getFullYear();
                    const displayMonth = this.currentDisplayDate.getMonth();
                    const displayYear = this.currentDisplayDate.getFullYear();

                    // 如果选中的周与当前显示月份没有交集，可以选择清除选中状态
                    const hasIntersection =
                        (weekStartYear === displayYear && weekStartMonth === displayMonth) ||
                        (weekEndYear === displayYear && weekEndMonth === displayMonth) ||
                        (weekStartYear < displayYear || (weekStartYear === displayYear && weekStartMonth < displayMonth)) &&
                        (weekEndYear > displayYear || (weekEndYear === displayYear && weekEndMonth > displayMonth));

                    if (!hasIntersection) {
                        // 保持选中状态，但日历会显示当前月份
                    }
                }
            } else if (this.mode === "monthly") {
                // 月报模式：不自动更新选中的月份，保持用户的选择
                // 用户需要点击日期来选择月份
            }

            // 触发自定义日历重新渲染
            this.$nextTick(() => {
                this.forceCalendarStyles();
            });
        },
        getCurrentMonthTitle() {
            // 始终基于当前显示的月份，而不是选中的日期
            const date = this.currentDisplayDate;
            const year = date.getFullYear();

            if (this.mode === 'yearly') {
                return `${year}年`;
            } else {
                const month = date.getMonth() + 1;
                return `${year}年${month}月`;
            }
        },
        goToToday() {
            const today = new Date();

            // 更新显示月份为今天所在的月份，使用该月的第一天
            this.currentDisplayDate = new Date(today.getFullYear(), today.getMonth(), 1);

            if (this.mode === "daily") {
                this.tempSelectedDate = new Date(today);
            } else if (this.mode === "weekly") {
                // 周报模式：选择今天，自动计算今天所在的周
                this.tempSelectedWeekDate = new Date(today);
                const startOfWeek = this.getStartOfWeek(today);
                const endOfWeek = new Date(startOfWeek);
                endOfWeek.setDate(startOfWeek.getDate() + 6);
                this.tempSelectedWeek = [startOfWeek, endOfWeek];
            } else if (this.mode === "monthly") {
                this.tempSelectedMonth = new Date(today);
            } else if (this.mode === "yearly") {
                this.tempSelectedYear = new Date(today);
                this.currentDisplayDate = new Date(today.getFullYear(), 0, 1);
                this.fetchYearlyDataAvailability();
            }

            // 触发自定义日历重新渲染
            this.$nextTick(() => {
                this.forceCalendarStyles();
            });
        },
        onDateSelect(date) {
            this.tempSelectedDate = date;

            // 如果选择的日期不在当前显示的月份中，自动切换显示月份
            const selectedMonth = date.getMonth();
            const selectedYear = date.getFullYear();
            const displayMonth = this.currentDisplayDate.getMonth();
            const displayYear = this.currentDisplayDate.getFullYear();

            if (selectedMonth !== displayMonth || selectedYear !== displayYear) {
                // 使用选中日期的年月，但设置为该月的第一天，避免日期溢出问题
                this.currentDisplayDate = new Date(selectedYear, selectedMonth, 1);
            }
        },
        onWeekDateSelect(date) {
            // 周报模式：用户选择一个日期，系统自动计算该日期所在的周范围
            this.tempSelectedWeekDate = date;

            // 计算该日期所在的周范围
            const startOfWeek = this.getStartOfWeek(date);
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            this.tempSelectedWeek = [startOfWeek, endOfWeek];

            // 如果选择的日期不在当前显示的月份中，自动切换显示月份
            const selectedMonth = date.getMonth();
            const selectedYear = date.getFullYear();
            const displayMonth = this.currentDisplayDate.getMonth();
            const displayYear = this.currentDisplayDate.getFullYear();

            if (selectedMonth !== displayMonth || selectedYear !== displayYear) {
                // 使用选中日期的年月，但设置为该月的第一天
                this.currentDisplayDate = new Date(selectedYear, selectedMonth, 1);
            }
        },
        onWeekSelect(dates) {
            // 保留此方法用于兼容性，但在新的单日选择模式下不会被调用
            if (dates.length === 2) {
                this.tempSelectedWeek = dates;
                this.tempSelectedWeekDate = new Date(dates[0]); // 使用周的开始日期

                const weekStartMonth = dates[0].getMonth();
                const weekStartYear = dates[0].getFullYear();
                const displayMonth = this.currentDisplayDate.getMonth();
                const displayYear = this.currentDisplayDate.getFullYear();

                if (weekStartMonth !== displayMonth || weekStartYear !== displayYear) {
                    this.currentDisplayDate = new Date(weekStartYear, weekStartMonth, 1);
                }
            }
        },
        onMonthSelect(date) {
            // 月报模式：保持用户选择的具体日期，不强制转换为月初
            this.tempSelectedMonth = new Date(date);

            // 如果选择的日期不在当前显示的月份中，自动切换显示月份
            const selectedYear = date.getFullYear();
            const selectedMonth = date.getMonth();
            const displayYear = this.currentDisplayDate.getFullYear();
            const displayMonth = this.currentDisplayDate.getMonth();

            if (selectedYear !== displayYear || selectedMonth !== displayMonth) {
                // 使用选中日期的年月，但设置为该月的第一天用于显示
                this.currentDisplayDate = new Date(selectedYear, selectedMonth, 1);
            }
        },

        // 年报模式：年份变化事件
        onYearChange(date) {
            this.currentDisplayDate = new Date(date.getFullYear(), 0, 1);
            // 年份变化时重新获取数据可用性
            this.fetchYearlyDataAvailability();
        },

        // 年报模式：月份选择事件
        onYearlyMonthSelect(date) {
            this.tempSelectedYear = new Date(date);
        },

        // 年份导航
        navigateYear(direction) {
            const currentYear = this.currentDisplayDate.getFullYear();
            const newYear = direction === 'prev' ? currentYear - 1 : currentYear + 1;

            // 检查年份范围
            if (this.minDate && newYear < this.minDate.getFullYear()) return;
            if (this.maxDate && newYear > this.maxDate.getFullYear()) return;

            // 更新显示年份
            this.currentDisplayDate = new Date(newYear, 0, 1);

            // 年份变化时重新获取数据可用性
            this.fetchYearlyDataAvailability();
        },
        cancel(check) {
            this.$emit("cancel", check);
        },
        confirm() {
            let result;
            if (this.mode === "daily") {
                result = this.tempSelectedDate;
            } else if (this.mode === "weekly") {
                // 周报模式：返回选中日期所在的周范围
                result = this.tempSelectedWeek;
            } else if (this.mode === "monthly") {
                // 月报模式：返回选中的具体日期（不再强制转换为月初）
                result = this.tempSelectedMonth;
            } else if (this.mode === "yearly") {
                // 年报模式：返回选中的年月日期
                result = this.tempSelectedYear;
            }
            this.$emit("confirm", result);
        },
        forceCalendarStyles() {
            // 强制修改日历样式
            const calendar = this.$el.querySelector('.van-calendar');
            if (calendar) {
                // 隐藏月份标题
                const monthTitles = calendar.querySelectorAll('.van-calendar__month-title, .van-calendar__month-mark');
                monthTitles.forEach(title => {
                    title.style.display = 'none';
                });

                // 修改选中日期颜色
                const selectedDays = calendar.querySelectorAll('.van-calendar__day--selected .van-calendar__day-text');
                selectedDays.forEach(day => {
                    day.style.backgroundColor = 'var(--emui_accent)';
                    day.style.background = 'var(--emui_accent)';
                    day.style.color = 'white';
                });

                // 设置容器高度
                const calendarBody = calendar.querySelector('.van-calendar__body');
                if (calendarBody) {
                    calendarBody.style.height = 'auto';
                    calendarBody.style.overflow = 'visible';
                }

                // 隐藏非第一个月份
                const months = calendar.querySelectorAll('.van-calendar__month');
                months.forEach((month, index) => {
                    if (index > 0) {
                        month.style.display = 'none';
                    }
                });
            }
        },

        // 获取年度数据可用性
        async fetchYearlyDataAvailability() {
            if (this.mode !== 'yearly') return;

            try {
                const year = this.currentDisplayDate.getFullYear();
                console.log(`获取${year}年数据可用性`);

                // 获取传感器配置 - 从父组件传递或使用默认配置
                const sensorConfig = this.getSensorConfig();
                if (!sensorConfig) {
                    console.warn('无法获取传感器配置，使用模拟数据');
                    this.yearlyDataAvailability = [1, 2, 3, 5, 8, 10, 12];
                    return;
                }

                // 优化：一次性获取整年的数据，然后分析哪些月份有数据
                const startTime = new Date(year, 0, 1); // 年初
                const endTime = new Date(year, 11, 31, 23, 59, 59, 999); // 年末

                const response = await this.$store.dispatch('getDeviceStatistics', {
                    startTime: Math.floor(startTime.getTime()),
                    endTime: Math.floor(endTime.getTime()),
                    sid: sensorConfig.sid,
                    character: sensorConfig.character,
                    accuracy: 'MONTHLY', // 使用月度精度
                    pageSize: 12 // 最多12个月的数据
                });

                const availableMonths = [];

                if (response && response.devDatas && response.devDatas.length > 0) {
                    // 解析返回的数据，提取有数据的月份
                    response.devDatas.forEach(item => {
                        if (item.time) {
                            const timestamp = this.parseStatisticsTime(item.time);
                            if (timestamp) {
                                const month = timestamp.getMonth() + 1; // 转换为1-12
                                if (!availableMonths.includes(month)) {
                                    availableMonths.push(month);
                                }
                            }
                        }
                    });

                    // 排序月份
                    availableMonths.sort((a, b) => a - b);
                }

                this.yearlyDataAvailability = availableMonths;
                console.log(`${year}年数据可用性:`, availableMonths);

            } catch (error) {
                console.error('获取年度数据可用性失败:', error);
                // 失败时使用空数组，表示没有数据
                this.yearlyDataAvailability = [];
            }
        },

        // 获取传感器配置
        getSensorConfig() {
            // 尝试从父组件获取传感器配置
            if (this.$parent && this.$parent.sensorConfig && this.$parent.type) {
                return this.$parent.sensorConfig[this.$parent.type];
            }

            // 默认使用CO2传感器配置
            return {
                sid: 'co2',
                character: 'current',
                dataType: 'co2'
            };
        },

        // 解析统计数据时间字符串
        parseStatisticsTime(timeString) {
            if (!timeString || typeof timeString !== 'string') {
                console.warn('无效的时间字符串:', timeString);
                return null;
            }

            try {
                if (timeString.length === 10) {
                    // yyyyMMddhh 格式
                    const year = parseInt(timeString.substring(0, 4));
                    const month = parseInt(timeString.substring(4, 6)) - 1; // 月份从0开始
                    const day = parseInt(timeString.substring(6, 8));
                    const hour = parseInt(timeString.substring(8, 10));
                    return new Date(year, month, day, hour, 0, 0, 0);
                } else if (timeString.length === 8) {
                    // yyyyMMdd 格式
                    const year = parseInt(timeString.substring(0, 4));
                    const month = parseInt(timeString.substring(4, 6)) - 1; // 月份从0开始
                    const day = parseInt(timeString.substring(6, 8));
                    return new Date(year, month, day, 0, 0, 0, 0);
                } else if (timeString.length === 6) {
                    // yyyyMM 格式（月度数据）
                    const year = parseInt(timeString.substring(0, 4));
                    const month = parseInt(timeString.substring(4, 6)) - 1; // 月份从0开始
                    return new Date(year, month, 1, 0, 0, 0, 0);
                } else {
                    console.warn('不支持的时间格式:', timeString);
                    return null;
                }
            } catch (error) {
                console.error('解析时间字符串失败:', error, timeString);
                return null;
            }
        },

        // 检查指定月份是否有数据
        async checkMonthDataAvailability(year, month, sensorConfig) {
            try {
                // 计算月份的时间范围
                const startTime = new Date(year, month - 1, 1);
                startTime.setHours(0, 0, 0, 0);

                const endTime = new Date(year, month, 0); // 该月最后一天
                endTime.setHours(23, 59, 59, 999);

                // 使用getDeviceStatistics API检查是否有数据
                const response = await this.$store.dispatch('getDeviceStatistics', {
                    startTime: Math.floor(startTime.getTime()), // 转换为秒级时间戳
                    endTime: Math.floor(endTime.getTime()),     // 转换为秒级时间戳
                    sid: sensorConfig.sid,
                    character: sensorConfig.character,
                    accuracy: 'DAILY',
                    pageSize: 1 // 只获取1条数据用于检查
                });

                // 如果有数据返回，说明该月有数据
                return response && response.devDatas && response.devDatas.length > 0;

            } catch (error) {
                console.warn(`检查${year}年${month}月数据可用性失败:`, error);
                return false;
            }
        },

        // 格式化为UTC时间字符串
        formatToUTC(date) {
            if (!date) return '';

            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(date.getUTCDate()).padStart(2, '0');
            const hours = String(date.getUTCHours()).padStart(2, '0');
            const minutes = String(date.getUTCMinutes()).padStart(2, '0');
            const seconds = String(date.getUTCSeconds()).padStart(2, '0');

            return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
        },
    },
};
</script>

<style lang="less">
// 日历内容区域
.calendar-content {
    padding: 0;
    flex: 1;
    min-height: 22.6rem;
}

// 年报模式的内容区域高度调整
.calendar-content.yearly-mode {
    min-height: 10rem;
}
</style>

<!-- 全局样式覆盖Vant组件 -->
<style lang="less">
// 使用更高优先级的选择器
.dialog .calendar-content .van-calendar {
    background-color: transparent !important;
    height: auto !important;
    overflow: visible !important;
}

.dialog .calendar-content .van-calendar .van-calendar__header {
    box-shadow: none !important;
}

.dialog .calendar-content .van-calendar .van-calendar__weekdays {
    padding: 0 2.4rem 0.8rem 2.4rem;
    background-color: transparent !important;
    color: var(--emui_text_primary);
    font-size: 1.4rem;

}

.dialog .calendar-content .van-calendar .van-calendar__body {
    padding: 0 2.4rem 0rem 2.4rem !important;
    background-color: transparent !important;
    height: auto !important;
    overflow: visible !important;
}

.dialog .calendar-content .van-calendar .van-calendar__month-title {
    display: none !important;
}

.dialog .calendar-content .van-calendar .van-calendar__month-mark {
    display: none !important;
}

.dialog .calendar-content .van-calendar .van-calendar__months {
    height: auto !important;
    overflow: visible !important;
}

.dialog .calendar-content .van-calendar .van-calendar__month:not(:first-child) {
    display: none !important;
}

.dialog .calendar-content .van-calendar .van-calendar__month {
    height: auto !important;
    overflow: visible !important;
}

.dialog .calendar-content .van-calendar .van-calendar__day {
    height: 4.4rem !important;
}

.dialog .calendar-content .van-calendar .van-calendar__day-text {
    font-size: 1.6rem !important;
    color: var(--emui_text_primary) !important;
    font-weight: 500 !important;
}

.dialog .calendar-content .van-calendar .van-calendar__day--disabled .van-calendar__day-text {
    color: var(--emui_text_secondary) !important;
}

.dialog .calendar-content .van-calendar .van-calendar__selected-day {
    background-color: var(--emui_accent) !important;
    background: var(--emui_accent) !important;
    color: white !important;
    border-radius: 50% !important;
    width: 3.2rem !important;
    height: 3.2rem !important;
    line-height: 3.2rem !important;
}


.dialog .calendar-content .van-calendar .van-calendar__day--today .van-calendar__day-text {
    position: relative !important;
}

.dialog .calendar-content .van-calendar .van-calendar__day--today .van-calendar__day-text:after {
    content: "" !important;
    position: absolute !important;
    bottom: 0.4rem !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 0.4rem !important;
    height: 0.4rem !important;
    background-color: var(--emui_accent) !important;
    border-radius: 50% !important;
}

.dialog .calendar-content .van-calendar .van-calendar__day--today.van-calendar__day--selected .van-calendar__day-text:after {
    display: none !important;
}


</style>

<style lang="less" scoped>
@import url("../style/public.less");

.red {
    color: var(--emui_functional_red);
}


// 自定义日历头部
.calendar-header {
    display: flex;
    align-items: center;
    justify-content: center; // 改为居中对齐
    position: relative; // 为绝对定位的今天按钮提供参考
    padding: 2.4rem 2.4rem 1.6rem 2.4rem;
    border-radius: 2.4rem 2.4rem 0 0; // 与容器保持一致

    .month-nav {
        display: flex;
        align-items: center;
        justify-content: center; // 居中对齐
        gap: 2.4rem; // 增大间距

        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.2rem;
            height: 2.4rem;
        }

        .month-title {
            font-size: 1.8rem;
            font-weight: 500;
            color: var(--emui_text_primary); // 强制深色文字
            min-width: 10rem; // 增加最小宽度确保居中
            text-align: center;
        }
    }

    .today {
        position: absolute; // 绝对定位到右侧
        right: 2.4rem;
        font-size: 1.6rem;
        color: var(--emui_accent);
        cursor: pointer;
        font-weight: 500;
    }
}
</style>
