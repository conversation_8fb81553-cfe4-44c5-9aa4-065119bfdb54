# LineChart集成任务

## 任务背景
用户新增了一个LineChart页面，希望将其引用到SubHome的类型2中，并使用静态数据渲染EChart图表。

## 执行计划
1. 修复LineChart.vue组件的问题
2. 在SubHome.vue中引入LineChart组件
3. 将LineChart放置到类型2的第一个tab中

## 已完成的工作

### 1. 修复LineChart.vue组件
- ✅ 添加了chartId属性，使用随机生成的唯一ID
- ✅ 在arr数组中添加了静态CO2数据：[420, 450, 380, 520, 480, 390, 460, 510, 440, 470, 500, 430]
- ✅ 修复了mounted方法中的renderChart调用，移除了不存在的this.type参数
- ✅ 改进了window.onresize事件处理，使用箭头函数并添加了null检查
- ✅ 修复了弃用的substr方法，改为使用substring

### 2. 在SubHome.vue中集成LineChart组件
- ✅ 导入了LineChart组件
- ✅ 在components中注册了LineChart组件
- ✅ 在类型2的第一个tab的chat-section中添加了LineChart组件

## 技术实现细节

### LineChart组件修改
```javascript
data () {
  return {
    arr: [420, 450, 380, 520, 480, 390, 460, 510, 440, 470, 500, 430], // 静态CO2数据
    myChart: null,
    chartId: 'lineChart_' + Math.random().toString(36).substring(2, 11) // 生成唯一ID
  }
}
```

### SubHome组件集成
```javascript
// 导入
import LineChart from "./LineChart.vue";

// 注册
components: {
  GaugeChart,
  LineChart,
}

// 使用
<div class="chat-section">
  <LineChart />
</div>
```

### EChart配置
- X轴：12小时时间标签（1h-12h）
- Y轴：数值范围0-9，间隔为3
- 数据：12个静态CO2数值点
- 样式：橙色线条，平滑曲线，无数据点标记
- 主题：支持暗黑模式

### 3. 添加数值显示区域
- ✅ 在LineChart上方添加了CO2数值显示
- ✅ 根据报表类型显示不同内容：
  - 日报：显示"当前日报"和超标状态
  - 周报：显示"周平均"和"平均值"
  - 月报：显示"月平均"和"平均值"
- ✅ 添加了颜色处理逻辑，使用Home组件的颜色方案
- ✅ 为所有三个tab都添加了LineChart组件

### 4. 样式优化
- ✅ 修复了LineChart组件的样式问题
- ✅ 添加了CO2状态颜色类
- ✅ 添加了数值显示区域的样式

### 5. 代码结构优化
- ✅ 移除了重复的selectIndex判断
- ✅ 创建了统一的co2-chart-section容器
- ✅ 使用.cardStyle()创建白色卡片效果
- ✅ 将数值显示和图表放在同一个卡片中
- ✅ 简化了代码结构，减少重复

### 6. 图表数据和显示修复
- ✅ 修复了超标状态的条件显示（只在日报中显示颜色）
- ✅ 修复了LineChart的Y轴范围（从0-9改为300-600适配CO2数据）
- ✅ 为LineChart添加了reportType props支持
- ✅ 根据不同报表类型显示不同数据：
  - 日报：24小时数据
  - 周报：7天数据
  - 月报：30天数据（12个点）
- ✅ 添加了watch监听reportType变化，自动重新渲染图表

### 7. 等级说明内容添加
- ✅ 在国际化文件中添加了等级说明相关文本
- ✅ 添加了中英文支持：
  - 中文：正常、超标、严重超标
  - 英文：Normal、Exceeded、Severely Exceeded
- ✅ 在SubHome中添加了等级说明UI组件
- ✅ 实现了带圆点的等级说明显示：
  - 绿色圆点：正常 x ≤ 1000 ppm
  - 黄色圆点：超标 1000 ppm < x ≤ 2000 ppm
  - 红色圆点：严重超标 x > 2000 ppm
- ✅ 添加了白色卡片样式，与其他组件保持一致

### 8. 类型3-5支持添加
- ✅ 为LineChart添加了dataType prop支持
- ✅ 根据dataType返回不同的数据和Y轴范围：
  - CO2: 300-600 ppm, 间隔100
  - 甲醛: 0.04-0.15 mg/m³, 间隔0.02
  - 温度: 15-35°C, 间隔5
  - 湿度: 30-60%, 间隔10
- ✅ 添加了甲醛等级计算逻辑和状态显示
- ✅ 实现了类型3（甲醛）的完整UI：
  - 数值显示+图表+等级说明
  - 甲醛等级范围：正常≤0.08, 超标0.08-0.12, 严重超标>0.12
- ✅ 实现了类型4、5（温湿度）的简化UI：
  - 只显示数值+图表，无状态指示
  - 显示"平均值"文本，无颜色状态
- ✅ 添加了甲醛等级说明的国际化文本

## 当前状态
✅ LineChart组件已修复并集成到SubHome的所有tab
✅ 静态数据已添加，图表可以正常渲染
✅ 数值显示区域已添加，支持不同报表类型
✅ 颜色处理已实现，符合Home组件的设计
⏳ 等待用户测试和反馈

## 下一步计划
1. 测试图表在SubHome类型2中的显示效果
2. 根据用户反馈调整样式和功能
3. 考虑添加动态数据支持
