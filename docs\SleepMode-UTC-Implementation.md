# 睡眠模式UTC时间格式实现

## 概述

根据您的最新要求，已完成睡眠模式定时器的UTC时间格式实现，支持：
- UTC时间格式 HHmmssZ (如 121200Z)
- 开启/关闭时间至少设置一个
- 一次性闹钟支持 (week=0)
- 参数变化自动上报

## 关键实现

### 1. UTC时间格式转换

```javascript
// 将分钟索引转换为UTC格式
convertTimeToUTCString(timeIndex) {
    if (this.isTimeNotSet(timeIndex)) {
        return "";  // 未设置时返回空字符串
    }
    
    const hour = Math.floor(timeIndex / 60);
    const minute = timeIndex % 60;
    const second = 0;  // 秒数固定为0
    
    return `${hour.toString().padStart(2, '0')}${minute.toString().padStart(2, '0')}${second.toString().padStart(2, '0')}Z`;
}

// 将UTC格式转换为分钟索引
convertUTCStringToTimeIndex(utcTimeString) {
    if (!utcTimeString || utcTimeString === "") {
        return null;  // 未设置
    }
    
    const timeStr = utcTimeString.replace('Z', '');
    
    if (timeStr.length >= 4) {
        const hour = parseInt(timeStr.substring(0, 2), 10);
        const minute = parseInt(timeStr.substring(2, 4), 10);
        
        if (!isNaN(hour) && !isNaN(minute) && hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
            return hour * 60 + minute;
        }
    }
    
    return null;
}
```

### 2. 时间设置验证

```javascript
// 验证时间设置 - 开启时间和关闭时间至少设置一个
const hasStartTime = !this.isTimeNotSet(this.startIndex);
const hasEndTime = !this.isTimeNotSet(this.endIndex);

if (!hasStartTime && !hasEndTime) {
    this.$toast("请至少设置开启时间或关闭时间");
    return;
}
```

### 3. 定时器数据构建

```javascript
// 获取UTC格式的时间字符串
const startTimeUTC = this.convertTimeToUTCString(this.startIndex);
const endTimeUTC = this.convertTimeToUTCString(this.endIndex);
const weekValue = this.generateWeekString();

const timerData = {
    mode: this.sleepModeEnabled ? 1 : 0,
    num: 1,
    timer: [{
        id: 1,
        enable: this.sleepModeEnabled ? 1 : 0,
        start: startTimeUTC,                // UTC格式: "220000Z"
        end: endTimeUTC,                    // UTC格式: "070000Z" 或 ""
        week: weekValue,                    // 0=一次性闹钟, 其他=重复闹钟
        sid: 'indicator',
        para: 'on',
        paraValue: this.sleepModeEnabled ? 0 : 1,
        sid2: '',
        para2: '',
        paraValue2: 0,
        sid3: '',
        para3: '',
        paraValue3: 0
    }]
};
```

### 4. 多格式解析支持

```javascript
// 支持UTC格式、数值和字符串格式的时间解析
parseTimeValue(timeValue) {
    if (timeValue == null || timeValue === undefined || timeValue === '') {
        return null;  // 返回null表示未设置
    }

    if (typeof timeValue === 'number') {
        return timeValue;  // 分钟数
    }

    if (typeof timeValue === 'string') {
        // UTC格式 "HHmmssZ"
        if (timeValue.endsWith('Z') && timeValue.length >= 5) {
            return this.convertUTCStringToTimeIndex(timeValue);
        }
        
        // 字符串格式 "HH:MM"
        if (timeValue.includes(':')) {
            const [hours, minutes] = timeValue.split(':').map(num => parseInt(num, 10));
            if (!isNaN(hours) && !isNaN(minutes)) {
                return hours * 60 + minutes;
            }
        }

        // 纯数字字符串
        const numValue = parseInt(timeValue, 10);
        if (!isNaN(numValue)) {
            return numValue;
        }
    }

    return null;
}
```

## 使用示例

### 基本睡眠模式 (22:00-07:00, 每日重复)

```javascript
// 设置参数
this.startIndex = 22 * 60;      // 22:00
this.endIndex = 7 * 60;         // 07:00
this.repeatIndex = 3;           // 每日重复
this.sleepModeEnabled = true;

// 生成的数据
{
    "mode": 1,
    "num": 1,
    "timer": [{
        "id": 1,
        "enable": 1,
        "start": "220000Z",     // UTC格式
        "end": "070000Z",       // UTC格式
        "week": 127,            // 每日重复
        "sid": "indicator",
        "para": "on",
        "paraValue": 0
    }]
}
```

### 一次性闹钟 (仅开启时间)

```javascript
// 设置参数
this.startIndex = 22 * 60;      // 22:00
this.endIndex = null;           // 不设置结束时间
this.repeatIndex = 0;           // 一次性闹钟
this.sleepModeEnabled = true;

// 生成的数据
{
    "mode": 1,
    "num": 1,
    "timer": [{
        "id": 1,
        "enable": 1,
        "start": "220000Z",     // 仅设置开始时间
        "end": "",              // 结束时间为空
        "week": 0,              // 一次性闹钟
        "sid": "indicator",
        "para": "on",
        "paraValue": 0
    }]
}
```

### 工作日睡眠模式 (23:30-06:30)

```javascript
// 设置参数
this.startIndex = 23 * 60 + 30; // 23:30
this.endIndex = 6 * 60 + 30;    // 06:30
this.repeatIndex = 1;           // 工作日重复
this.sleepModeEnabled = true;

// 生成的数据
{
    "mode": 1,
    "num": 1,
    "timer": [{
        "id": 1,
        "enable": 1,
        "start": "233000Z",     // 23:30:00 UTC
        "end": "063000Z",       // 06:30:00 UTC
        "week": 31,             // 工作日重复
        "sid": "indicator",
        "para": "on",
        "paraValue": 0
    }]
}
```

## 时间格式对照表

| 界面显示 | 分钟索引 | UTC格式 | 说明 |
|----------|----------|---------|------|
| 00:00 | 0 | 000000Z | 午夜 |
| 07:30 | 450 | 073000Z | 早上7点30分 |
| 12:12 | 732 | 121200Z | 中午12点12分 |
| 22:00 | 1320 | 220000Z | 晚上10点 |
| 23:59 | 1439 | 235900Z | 晚上11点59分 |

## 验证和调试

### 控制台输出示例

```javascript
睡眠模式定时器下发数据: {
    "mode": 1,
    "num": 1,
    "timer": [{
        "id": 1,
        "enable": 1,
        "start": "220000Z",
        "end": "070000Z",
        "week": 127,
        "sid": "indicator",
        "para": "on",
        "paraValue": 0
    }]
}

定时器参数详情: {
    "模式": "开启",
    "开始时间UTC": "220000Z",
    "结束时间UTC": "070000Z",
    "重复设置": 127,
    "重复类型": "重复闹钟",
    "重复二进制": "1111111",
    "时间验证": {
        "开始时间有效": true,
        "结束时间有效": true,
        "至少一个时间": true
    }
}
```

## 关键特性

✅ **UTC时间格式**: 完全支持HHmmssZ格式  
✅ **灵活时间设置**: 开启/关闭时间至少设置一个  
✅ **一次性闹钟**: week=0时作为一次性闹钟处理  
✅ **多格式兼容**: 支持UTC、数值、字符串格式解析  
✅ **参数验证**: 完整的输入验证和错误处理  
✅ **调试支持**: 详细的日志输出和测试功能  
✅ **状态上报**: 参数变化时自动上报  

## 文件修改清单

1. **src/views/SleepPage.vue** - 主要实现文件
2. **docs/SleepMode-Timer-Configuration.md** - 更新配置说明
3. **test/sleep-timer-test.js** - 更新测试文件
4. **docs/SleepMode-UTC-Implementation.md** - 本实现文档

现在睡眠模式定时器完全符合您的UTC时间格式要求，可以直接使用！
