# Spinner

#### 下来框选

| prop参数 | 描述 |
| --- | --- |
| show    | 是否显示, Bool|
| values   |选项值列表, Array|
| value   |当前选择的值, Number|

| Event | 描述 |
| --- | --- |
| cancel   |取消事件|
| confirm   |确定事件|

#### 与ControlBar一起使用示例

#### ![](./images/spinner.png)

```` JavaScript
timingValues: [
  {name: '5', value: 0},
  {name: '10', value: 1},
  {name: '15', value: 2},
  {name: '20', value: 3},
  {name: '25', value: 4},
  {name: '30', value: 5}
]
<ControlBar
    :name="$t('Timing')"
    iconName="ic_timing"
    :disabled="!(canControl && Switch == 1)"
    :info="timingText"
    @handleClick="isShowTimingPicker = true"
    :active="Timing >= 0 && canControl && Switch == 1">
    <Spinner :values="timingValues" :value="Timing" :show="isShowTimingPicker" @cancel="isShowTimingPicker = false" @confirm="onTimingConfirm"></Spinner>
</ControlBar>
````
