/* eslint-disable id-length */
import state from './state';
const actions = {
    // desc 设置标题不可见
    setTitleVisible({ commit }, isVisible) {
        return new Promise((resolve, reject) => {
            window.hilink && window.hilink.setTitleVisible(isVisible, 'Ag.showTitleRes');
            window.Ag.showTitleRes = res => {
                let data = JSON.hParse(res);
                Ag.isError(data) ? reject(new Error('showTitleRes error')) : resolve();
            };
        });
    },
    // desc 设置原生title文字颜色和背景颜色(包括最顶上的显示时间的部分)
    modifyTitleBar({ commit }, param) {
        const { isColorWhite = state.isDarkMode, bg = state.isDarkMode ? '#ffffff' : '#000000' } = param || {};
        return new Promise((resolve, reject) => {
            window.hilink && window.hilink.modifyTitleBar
                && window.hilink.modifyTitleBar(isColorWhite, bg, 'Ag.modifyTitleBarRes');
            window.Ag.modifyTitleBarRes = res => {
                let data = JSON.hParse(res);
                Ag.isError(data) ? reject(new Error('hiddenTitile error')) : resolve();
            };
        });
    },
    // desc 获取状态栏高度
    getStatusBarHeight({ commit }) {
        return new Promise((resolve, reject) => {
            window.hilink && window.hilink.getStatusBarHeight('Ag.getStatusBarHeightRes');
            window.Ag.getStatusBarHeightRes = res => {
                let data = JSON.hParse(res);
                if (Ag.isError(data)) {
                    reject(new Error('getStatusBarHeight error'));
                } else {
                    commit('commitAppData', { statusBarHeight: data.statusBarHeight });
                    resolve();
                }
            };
        });
    },
    // desc 获取手机信息,包含系统和屏幕
    getPhoneInfo({ commit }) {
        let type = 'android';
        let version = '10.0.0';
        let isPad = false;
        let isDarkMode = false;
        let DPR = window.devicePixelRatio;
        let changeScreen = false;
        try {
            let ua = navigator.userAgent.toLowerCase();
            let reg, matchInfo;
            if (ua.indexOf('like mac os x') > 0) {
                reg = /os [\d._]+/gi;
                type = 'ios';
            } else if (ua.indexOf('android') > 0) {
                reg = /android [\d._]+/gi;
                type = 'android';
            } else if (ua.indexOf('harmony') > 0) {
                reg = /harmony [\d._]+/gi;
                type = 'harmony';
            }
            matchInfo = ua.match(reg);
            version = String(matchInfo).replace(/[^0-9|_.]/gi, '').replace(/_/gi, '.');
            let isScreenSpreaded = window.hilink && window.hilink.isScreenSpreaded && window.hilink.isScreenSpreaded();
            changeScreen = isScreenSpreaded;
            // isPad = /(?:ipad|playbook)/.test(ua)
            //     || (['android', 'harmony'].indexOf(type) !== -1 && !(/(?:mobile)/.test(ua)));
            let padLandscape = window.hilink.getPadLandscape ? window.hilink.getPadLandscape() : -1;
            isPad = padLandscape != -1;
            if (type === 'ios') {
                window.hilink.getDarkMode('Ag.getDarkMode');
                window.Ag.getDarkMode = res => {
                    Number(res) === 2 && (isDarkMode = true);
                    commit('commitAppData', { isDarkMode });
                };
            } else {
                window.hilink && window.hilink.getDarkMode && window.hilink.getDarkMode() === 2 && (isDarkMode = true);
            }
        } catch (err) {
            console.hLog('通过 navigator.userAgent 获取系统类型和版本报错,使用默认配置android 和 10');
        }
        console.hLog(new Time().logTime,
            `获取type为${type + version}, ${(isPad ? '平板' : '手机') + ',' + (changeScreen ? '折叠展开' : '未展开')}}`
        );
        let browserWidth = document.documentElement.clientWidth;
        if (changeScreen || isPad) { // 折叠屏展开/Pad竖屏
            document.documentElement.setAttribute('style', 'font-size:10px');
        } else { // 其他
            document.documentElement.setAttribute('style', `font-size:${browserWidth / 36}px`);
        }
        // document.documentElement.setAttribute('style', `font-size:${changeScreen ? 10 : (browserWidth / 36)}px`);
        commit('commitAppData', { type, version, isPad, isDarkMode, DPR, changeScreen });
    },
    // desc 下发指令统一通道
    // param type:Object,required:true,default:{sid:{cha:1}}
    setDevInfo({ commit }, param) {
        return new Promise((resolve, reject) => {
            console.hLog(new Time().logTime + '下发数据', param);
            commit('commitAppData', { showTaskToast: true });
            if (window.hilink) {
                window.hilink.setDeviceInfo('0', JSON.stringify(param), 'Ag.setDevInfoRes');
                console.hLog(new Time().logTime + '下发数据string', JSON.stringify(param));
            } else { // 模拟下发用
                setTimeout(() => {
                    commit('commitAppData', { showTaskToast: false });
                    resolve();
                }, 100);
                setTimeout(() => {
                    commit('commitMock', param);
                }, 300);
            }
            window.Ag.setDevInfoRes = res => {
                commit('commitAppData', { showTaskToast: false });
                let data = JSON.hParse(res);
                console.hLog(new Time().logTime + '返回命令错误码', res);
                Ag.isError(data) ? reject(new Error('error')) : resolve();
            };
        });
    },
    // desc 向云端获取缓存的所有数据
    getDevInfoAll({ commit, dispatch }) {
        return new Promise((resolve, reject) => {
            if (!window.hilink) {
                commit('commitAppData', { hasGetAllData: true });
                return;
            }
            window.hilink.getDevCacheAll('0', '', 'Ag.getDevCacheAllRes');
            window.Ag.getDevCacheAllRes = res => {
                let data = JSON.hParse(res);
                console.hLog(new Time().logTime + 'app本地缓存', data);
                if (typeof data.role !== 'undefined' && data.role !== 'owner') {
                    state.shareDevice = true;
                }
                commit('commitAppData', {
                    devId: data.devId,
                    devName: data.devName,
                    sn: data.devInfo.sn,
                    deviceModel: data.devInfo.model,
                    role: data.role,
                    roomName: data.roomName,
                    status: data.status,
                    registerTime: data.registryTime
                });
            };
            // 获取设备状态(最新)
            window.hilink.getDevInfoAll('0', '', 'Ag.getDevInfoAllRes');
            console.hLog(new Time().logTime + '获取云端快照');
            window.Ag.getDevInfoAllRes = res => {
                let data = JSON.hParse(res);
                commit('commitAppData', { hasGetAllData: true, showTaskToast: false });
                console.hLog(new Time().logTime + 'getDevInfoAllRes', data);
                data.services.forEach(service => commit('commitHilink', service));
                // 数据有变动主动上报
                window.deviceEventCallback = res => {
                    let data = JSON.hParse(res);
                    console.hLog(new Time().logTime + '设备上报', data);
                    data.sid && commit('commitHilink', data);
                };
                resolve();
            };
        });
    },

    // 跳转原生页面
    jumpTo({ commit }, pathStr) {
        return new Promise((resolve, reject) => {
            window.hilink && window.hilink.jumpTo(pathStr, 'Ag.jumpToRes');
            window.Ag.jumpToRes = res => {
                const data = JSON.hParse(res);
                Ag.isError(data) ? reject(new Error('error')) : resolve();
            };
        });
    },
    // 调用原生toast
    toast({ commit }, toastStr) {
        window.hilink ? window.hilink.toast(2, toastStr) : alert('str');
    },
    // 删除设备
    deleteDev({ dispatch }) {
        window.hilink && window.hilink.deleteDevice(true, 'Ag.deleteDevRes');
        window.Ag.deleteDevRes = res => {
            const data = JSON.hParse(res);
            Ag.isError(data) || dispatch('closeWebview');
        };
    },
    // 获取摆放位置
    getRoomList({ commit }) {
        window.hilink && window.hilink.getRoomList('Ag.getRoomListRes');
        window.Ag.getRoomListRes = res => {
            let data = JSON.hParse(res);
            commit('commitAppData', { roomList: data.roomList });
        };
    },
    // 拉起设备信息界面
    jumpToDeviceInfo({ dispatch }) {
        window.hilink && window.hilink.jumpTo('com.huawei.smarthome.deviceinfoactivity', 'Ag.jumpToDeviceInfoResult');
        window.Ag.jumpToDeviceInfoResult = res => {
        };
    },
    jumpToDeviceSetting({ dispatch }) {
        window.hilink && window.hilink.jumpTo('com.huawei.smarthome.deviceSettingActivity', 'Ag.jumpToDeviceInfoResult');
        window.Ag.jumpToDeviceSettingResult = res => {
        };
    },
    finishDeviceActivity() {
        window.hilink && window.hilink.finishDeviceActivity()
    },
    // desc 设置设备名称,后台提供的接口不一致
    setDevName({ commit, dispatch }, nameStr) {
        return new Promise((resolve, reject) => {
            window.hilink
                ? window.hilink.modifyDeviceName(nameStr, 'Ag.setDevNameRes')
                : commit('commitAppData', { devName: nameStr });
            console.hLog(new Time().logTime + '设置名称为', nameStr);
            window.Ag.setDevNameRes = res => {
                console.hLog(new Time().logTime + '返回修改设备名称结果', res);
                let data = JSON.hParse(res);
                Ag.isError(data) ? reject(new Error('error')) : resolve();
            };
        });
    },
    // 修改设备位置
    modifyDeviceRoomName({ commit, dispatch }, roomName) {
        return new Promise((resolve, reject) => {
            window.hilink
                ? window.hilink.modifyDeviceRoomName(roomName, 'Ag.modifyDeviceRoomNameRes')
                : commit('commitAppData', { roomName: roomName });
            console.hLog(new Time().logTime + '设置房间位置为', roomName);
            window.Ag.modifyDeviceRoomNameRes = res => {
                console.hLog(new Time().logTime + '返回修改设备房间位置结果', res);
                let data = JSON.hParse(res);
                Ag.isError(data) ? reject(new Error('error')) : resolve();
            };
        });
    },
        // desc 获取设备统计数据
    getDeviceStatistics({ commit, state }, {
        startTime,  // 秒级时间戳
        endTime,    // 秒级时间戳
        sid,        // serviceId
        character,  // 属性名
        accuracy = 'HOUR',  // HOUR或DAILY
        pageSize = 100      // 1-100
    }) {
        return new Promise((resolve, reject) => {
            if (!window.hilink) {
                reject(new Error('hilink not found'));
                return;
            }

            // 参数验证
            if (!startTime || !endTime || !sid || !character) {
                reject(new Error('Missing required parameters'));
                return;
            }

            if (!['HOUR', 'DAILY'].includes(accuracy)) {
                reject(new Error('accuracy must be HOUR or DAILY'));
                return;
            }

            // 验证pageSize范围
            const numPageSize = Number(pageSize);
            if (isNaN(numPageSize) || numPageSize < 1 || numPageSize > 100) {
                reject(new Error('pageSize must be a number between 1 and 100'));
                return;
            }

            const request = {
                sid,
                character,
                startTime: String(startTime),
                endTime: String(endTime),
                accuracy,
                pageSize: numPageSize // 使用验证后的数字类型pageSize
            };
            console.log('📊 getDeviceStatistics API请求参数:', JSON.stringify(request, null, 2));
            console.log('🕐 时间戳验证:', {
                startTime: `${startTime} (${new Date(startTime * 1000).toLocaleString()})`,
                endTime: `${endTime} (${new Date(endTime * 1000).toLocaleString()})`,
                accuracy: accuracy,
                pageSize: numPageSize
            });
            window.hilink.getDevStatisticsDataWithQuery(
                state.devId,
                JSON.stringify(request),
                'Ag.getDevStatisticsDataWithQueryRes'
            );

            window.Ag.getDevStatisticsDataWithQueryRes = res => {
                let data = JSON.hParse(res);
                console.hLog(new Time().logTime + '获取统计数据结果', data);

                if (Ag.isError(data)) {
                    reject(new Error('get statistics error'));
                    return;
                }
                resolve(data);
            };
        });
    },

        // 复写物理按键
    overrideBackPressed(context, val) {
        window.hilink && window.hilink.overrideBackPressed(val, 'Ag.goBack');
    },

    // desc 获取设备历史数据
    getDevHistory({ commit, state }, {
        pageNo = '0',       // 页码数，缺省为0
        pageSize = '100',   // 每页数据个数，缺省为10
        startTime,          // UTC时间格式：yyyyMMddTHHmmssZ
        endTime,            // UTC时间格式：yyyyMMddTHHmmssZ
        sid,                // serviceId
        character           // characteristicName
    }) {
        return new Promise((resolve, reject) => {
            if (!window.hilink) {
                reject(new Error('hilink not found'));
                return;
            }

            // 参数验证
            if (!startTime || !endTime || !sid || !character) {
                reject(new Error('Missing required parameters'));
                return;
            }

            console.log('getDevHistory request:', {
                devId: state.devId,
                pageNo,
                pageSize,
                startTime,
                endTime,
                sid,
                character
            });

            window.hilink.getDevHistory(
                '0',        // devId 固定为"0"表示当前设备
                pageNo,
                pageSize,
                startTime,
                endTime,
                sid,
                character,
                'Ag.getDevHistoryRes'
            );

            window.Ag.getDevHistoryRes = res => {
                let data = JSON.hParse(res);
                console.hLog(new Time().logTime + '获取历史数据结果', data);

                if (Ag.isError(data)) {
                    reject(new Error('get history data error'));
                    return;
                }
                resolve(data);
            };
        });
    },

    // 获取app版本
    getAppVersionCode() {
        console.log("getAppVersionCode")
        try {
            if (window.hilink.getSystemInfoSync) {
                window.hilink.getSystemInfoSync('bleAppVersionCodeCallback');
                window.bleAppVersionCodeCallback = res => {
                    const data = jsonParse(res);
                    let version = String(data.version);
                    if (version.length >= 10 && !version.includes('.')) {
                        version = version.slice(3);
                    }
                    window.APPVersionCode = version;
                };
            }
        } catch (error) {
            console.log('getAppVersionCode error');
        }
    },
    // desc 当前app版本是否大于等于目标版本
    isLaterAppVersion({
        dispatch
    }, targetVersion) {
        dispatch('getAppVersionCode');
        const curVersion = String(window.APPVersionCode || '13.0.0.0');
        let isLater = false;
        if (curVersion.includes('.')) {
            const target = targetVersion.split('.');
            const current = curVersion.split('.');
            let length = current.length;
            if (current.length >= target.length) {
                isLater = true;
                length = target.length;
            }
            for (let i = 0; i < length; i++) {
                if (Number(current[i]) !== Number(target[i])) {
                    isLater = Number(current[i]) > Number(target[i]);
                    break;
                }
            }
        } else {
            let newVersion = String(targetVersion).replace(/\./g, '');
            isLater = Number(curVersion) >= Number(newVersion);
        }
        return isLater;
    }
};
export default actions;
