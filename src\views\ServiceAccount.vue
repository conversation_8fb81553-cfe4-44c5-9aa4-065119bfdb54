<!--
 * @Author: Augment Agent
 * @Date: 2025-07-09
 * @LastEditTime: 2025-07-09
 * @LastEditors: Augment Agent
 * @Description: 豪恩服务号页面
-->
<template>
  <div id="service-account-page">
    <Titlebar :title="$t('service_account_page_title')" :showRightIcon="false" @leftClick="$router.goBack()" />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      
      <!-- 二维码区域 -->
      <div class="qrcode-section">
        <div class="qrcode-card">
          <img src="../assets/qrcode.jpg" class="qrcode-image" alt="豪恩服务号二维码" />
        </div>
      </div>

      <!-- 方式一 -->
      <div class="method-section">
        <ListContainer style="width: auto;padding: 0;margin: 0;">
          <div class="method-item">
            <div class="method-content">
              <div class="method-title">{{ $t('method_one') }}</div>
              <div class="method-desc">{{ $t('copy_haoen_text') }}</div>
            </div>
            <div class="copy-btn" @click="copyHaoenText">{{ $t('copy_button') }}</div>
          </div>
        </ListContainer>
      </div>

      <!-- 方式二 -->
      <div class="method-section">
        <ListContainer style="width: auto;padding: 0;margin: 0;">
          <div class="method-item" style="height: 6.6rem">
            <div class="method-content">
              <div class="method-title">{{ $t('method_two') }}</div>
              <div class="method-desc">{{ $t('screenshot_qrcode_text') }}</div>
            </div>
          </div>
        </ListContainer>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { goBack } from '../util/mixins';

export default {
  name: 'ServiceAccount',
  mixins: [goBack],
  data() {
    return {
      dialogList: [] // 没有弹窗，空数组
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight'])
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
    // 复制"豪恩"文本到剪贴板
    copyHaoenText() {
      const textToCopy = '豪恩';
      
      // 决策理由：使用现代浏览器的Clipboard API进行复制操作
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(textToCopy).then(() => {
          this.showCopySuccess();
        }).catch(() => {
          this.fallbackCopy(textToCopy);
        });
      } else {
        this.fallbackCopy(textToCopy);
      }
    },
    // 备用复制方法
    fallbackCopy(text) {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      try {
        document.execCommand('copy');
        this.showCopySuccess();
      } catch (err) {
        console.error('复制失败:', err);
      } finally {
        document.body.removeChild(textArea);
      }
    },
    // 显示复制成功提示
    showCopySuccess() {
      // 决策理由：使用Toast提示用户复制成功
      this.$toast(this.$t('copy_success'));
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/ServiceAccount.less");
</style>
