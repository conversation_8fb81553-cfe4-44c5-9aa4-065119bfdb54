<template>
  <div class="error-location" v-if="showErrorMessage">
    <div class="error-container">
      <img class="error-image" :src="require('../../lib/assets/ic_error.png')">
      <span class="error-text">{{ errorMessage }}</span>
    </div>
  </div>
</template>
<script>

export default {
  props: {

  },
  name: '<PERSON><PERSON>',
  data(){
    return {
      showErrorMessage: false,
      errorMessage: ''
    }
  },
  methods: {
    show(message) {
      this.showErrorMessage = true
      this.errorMessage = message
    },
    dismiss() {
      this.showErrorMessage = false
    }
  }
};
</script>

<style lang="less" scoped>
.error-location {
   position: relative;
   width: 100%;

   .error-container {
     border-radius: 1.8rem;
     flex-direction: row;
     display: flex;
     align-items: center;
     margin-left: 1.2rem;
     margin-right: 1.2rem;
     padding: 1.2rem;
     background-color: var(--color_tip_error);

     .error-image {
       width: 2.4rem;
       height: 2.4rem;
     }

     .error-text {
       font-size: 1.6rem;
       margin-left: 1.6rem;
       color: var(--emui_functional_red);
     }
   }
 }
</style>
