{"CommonFaultDetectionCode_0": "Normal", "CommonFaultDetectionCode_100": "Temperature and humidity sensor fault", "CommonFaultDetectionCode_101": "HCHO sensor fault", "CommonFaultDetectionCode_102": "CO2 sensor fault", "CommonFaultDetectionCode_104": "Battery not installed", "BatteryAlarm_1": "Battery power is low, please charge in time", "Co2Current_sub": "CO2", "Co2Current_unit": "ppm", "Co2Level_sub": "", "Co2Level_unit": "", "item_Co2Level_1": "Normal", "item_Co2Level_2": "Exceeded", "item_Co2Level_3": "Severely Exceeded", "HchoCurrent_sub": "HCHO", "HchoCurrent_unit": "mg/m³", "HchoLevel_sub": "", "HchoLevel_unit": "", "item_HchoLevel_1": "Normal", "item_HchoLevel_2": "Exceeded", "item_HchoLevel_3": "Severely Exceeded", "HeatCurrentFloat_sub": "Temperature", "HeatCurrentFloat_unit": "℃", "MoistureCurrent_sub": "<PERSON><PERSON><PERSON><PERSON>", "MoistureCurrent_unit": "%", "ScreenOn": "Smart Screen Off", "ScreenOn_tip": "When enabled, the screen will turn off after the set time when no one is around.\nThe screen will turn on again when you touch the device button or sense someone approaching", "dialog_title_ScreenOn": "Screen Always-On Time", "dialog_subtitle_ScreenOn": "Set the time to turn off the screen when no one is around", "screen_time_disabled": "Disabled", "screen_time_30s": "30 seconds", "screen_time_1m": "1 minute", "screen_time_2m": "2 minutes", "screen_time_3m": "3 minutes", "screen_time_4m": "4 minutes", "screen_time_5m": "5 minutes", "Timer": "Sleep Mode", "Timer_tip": "Click Sleep Mode to set Sleep Mode, Alarm Light Display, and the start and end time", "current_indoor_environment": "Current Indoor Environment Quality", "comprehensive_data_reference": "Comprehensive data calculation results, for reference only", "quality_excellent": "Excellent", "quality_good": "Good", "quality_poor": "Poor", "device_offline": "Device Offline", "enviroment_title": "Environment", "co2_title": "CO2", "hcho_title": "HCHO", "temperature_title": "Temperature", "humidity_title": "<PERSON><PERSON><PERSON><PERSON>", "daily_report": "Daily", "weekly_report": "Weekly", "monthly_report": "Monthly", "yearly_report": "Yearly", "daily_average_quality": "Today's Average Quality", "weekly_average_quality": "This Week's Average Quality", "monthly_average_quality": "This Month's Average Quality", "yearly_average_quality": "This Year's Average Quality", "select_date": "Select Date", "select_week": "Select Week", "select_month": "Select Month", "select_year": "Select Year", "today": "Today", "this_week": "This Week", "this_month": "This Month", "confirm": "Confirm", "date_format_daily": "YYYY-MM-DD", "date_format_weekly": "YYYY-MM-DD - MM-DD", "date_format_monthly": "YYYY-MM", "today_data": "Today's Data", "weekly_average_data": "Weekly Average Data", "monthly_average_data": "Monthly Average Data", "yearly_average_data": "Yearly Average Data", "average_temp": "Average Temperature", "average_humi": "Average Humidity", "level_area_desc": "Level Range Description", "cuurent_daily": "Daily Average", "weekly_average": "Weekly Average", "monthly_average": "Monthly Average", "level_desc_intro": "Different data levels represent different values. The following is a description of the specific monitoring data ranges included in each level.", "level_normal": "Normal", "level_exceeded": "Exceeded", "level_severely_exceeded": "Severely Exceeded", "average_value": "Average", "moisture_level_dry": "Dry", "moisture_level_comfortable": "Comfortable", "moisture_level_humid": "<PERSON><PERSON><PERSON>", "moisture_level_comfortable_range": "30% ≤ x ≤ 70%", "moisture_level_dry_range": "x < 30%", "moisture_level_humid_range": "x > 70%", "level_normal_range": "x ≤ 1000 ppm", "level_exceeded_range": "1001 ppm < x ≤ 2000 ppm", "level_severely_exceeded_range": "x > 2000 ppm", "hcho_level_normal_range": "x ≤ 0.08 mg/m³", "hcho_level_exceeded_range": "0.09 mg/m³ < x ≤ 0.2 mg/m³", "hcho_level_severely_exceeded_range": "x > 0.2 mg/m³", "sleep_title": "Sleep Mode Settings", "start_time": "Start Time", "end_time": "End Time", "no_setting": "Not Set", "sleep_mode": "Sleep Mode", "repeat": "Repeat", "alarm_light_display": "Alarm Light Display", "alarm_light_desc": "Display alarm light when CO2, HCHO, etc. exceed the standard in sleep mode", "no_repeat": "No Repeat", "run_once": "Run Once", "week_repeat": "Weekdays (Mon-Fri)", "weekend_repeat": "Weekend", "daily_repeat": "Daily", "custom_repeat": "Custom", "please_enable_sleep_mode": "Please enable sleep mode first", "please_set_time": "Please set start and end time", "device_not_controllable": "Device is not controllable", "save_success": "Save successful", "save_failed": "Save failed, please try again", "save": "Save", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "custom_week_title": "Custom Repeat", "custom_week_subtitle": "Select repeat days", "select_all": "Select All", "deselect_all": "Deselect All", "next_day": "Next day", "time_not_set": "Time not set", "saleservice_title": "After-sales Service", "product_title": "Product Wiki", "after_sales_service": "After-sales Service", "service_description": "Hotline | FAQ | Online Chat", "customer_service_hotline": "Customer Service Hotline", "service_time": "Service Time: 9:00-18:00", "frequently_asked_questions": "Frequently Asked Questions", "online_consultation": "Online Consultation", "start_chat": "Start Online Chat", "chat_description": "Professional support for you", "product_wiki": "Product Wiki", "product_description": "User Guide | Features | FAQ", "user_guide": "User Guide", "voice_control_guide": "Voice Control Guide", "common_questions": "FAQ", "product_features": "Features", "service_consultation": "Service Consultation", "wiki_guide_title": "User Guide", "wiki_quick_start": "Quick Start", "wiki_usage_tips": "Usage Tips/Precautions", "wiki_air_quality": "Air Quality Encyclopedia", "wiki_troubleshooting": "Troubleshooting", "wiki_faq_title": "Frequently Asked Questions", "wiki_faq_1": "Q: Is internet connection required?", "wiki_faq_2": "Q: What is the detection range of one device?", "wiki_faq_3": "Q: What third-party tests has the device passed?", "wiki_faq_4": "Q: Feels inaccurate, can it be calibrated or reset?", "wiki_faq_5": "Q: Why is the detection data different from other similar products?", "wiki_faq_6": "Q: Temperature and humidity display issues when first used/not used for a long time?", "wiki_faq_7": "Q: Formaldehyde detection data remains high when using cosmetics/perfume/toilet water nearby?", "wiki_faq_8": "Q: No odor but air quality exceeds standard? How does the sensor define air quality exceeding standard? What to do when air quality exceeds standard?", "wiki_faq_9": "Q: Why does the screen automatically turn off? How to light up the screen when it's off?", "wiki_faq_10": "Q: How long can the backup battery last?", "wiki_faq_11": "Q: How far is the automatic screen-off sensing distance?", "wiki_faq_12": "Q: Why does smart screen-off trigger falsely/insensitively?", "wiki_faq_13": "Q: What is the after-sales service policy?", "product_full_name": "HarmonyOS Haoen Four-in-One Air Quality Sensor", "serial_number": "Serial Number: ", "scan_device_info": "Scan to get device serial number information", "scan_device_info_desc": "For service personnel to quickly obtain device information", "consultation": "Consultation", "service_hotline": "Haoen Service Hotline", "hotline_desc": "One-on-one support", "online_customer_service": "Online Customer Service", "online_service_desc": "Online chat, real-time support", "smart_curtain_service": "Haoen Smart Curtain Service", "third_party_service_title": "You are about to be redirected to a third-party service page. Continue?", "third_party_service_message": "The information you fill in will be collected and processed by third-party service providers. Smart Life will not collect your personal information.", "battery_alarm_title": "<PERSON> Reminder", "battery_alarm_message": "The device power has switched to battery consumption mode. In this mode, the \"Auto Screen Off and Sleep Mode\" function will be disabled (default set to 30 seconds auto screen off). Please be aware!", "battery_alarm_dont_remind": "Don't remind me again", "battery_alarm_confirm": "Got it", "service_hotline_page_title": "Haoen Service Hotline", "service_phone_number": "************", "service_working_hours": "Sunday to Saturday 9:00-22:00 (Normal duty on legal holidays except Spring Festival)", "service_account_page_title": "Haoen Service Account", "method_one": "Method 1", "method_two": "Method 2", "copy_haoen_text": "<PERSON><PERSON> \"Haoen\" to clipboard, open WeChat to search and follow the official service account", "screenshot_qrcode_text": "Screenshot and save locally, open WeChat to scan QR code and follow the official service account", "copy_button": "Copy", "copy_success": "Copied to clipboard", "wiki_quick_start_title": "Quick Start", "wiki_usage_tips_title": "Usage Tips/Precautions", "wiki_air_quality_title": "Air Quality Encyclopedia", "wiki_troubleshooting_title": "Troubleshooting", "wiki_faq_title_page": "FAQ", "no_data": "No Data"}