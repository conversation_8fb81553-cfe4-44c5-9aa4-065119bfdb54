import VueI18n from 'vue-i18n';
import Vue from 'vue';
Vue.use(VueI18n);

import enLocale from '../../lib/i18n/language/zh.json'
import zhLocale from'../../lib/i18n/language/en.json'
import en from './language/zh.json'
import zh from'./language/en.json'

function getAppLanguage() {
    let language,
        reg = /^zh-/i;
    let DEFAULT_LANGUAGE = 'zh-CN';
    if(localStorage && localStorage.getItem('AppLanuage')) {
        language = localStorage.getItem('AppLanuage');
    } else if (window.hilink && window.hilink.getAppLanguageSync) {
        language = window.hilink.getAppLanguageSync();
    } else if (navigator && navigator.language) {
        language = navigator.language;
    } else {
        language = DEFAULT_LANGUAGE;
    }
    language = String(language).toLowerCase();
    language = reg.test(language) || language === 'bo-cn' ? 'zh-CN' : 'en-US';
    return language;
}


const i18n = new VueI18n({
    // locale: getAppLanguage(),
    locale: 'zh-CN',
    messages: {
        'zh-CN': Object.assign(en, enLocale),
        'en-US': Object.assign(zh, zhLocale)
    }
});
export default i18n;
