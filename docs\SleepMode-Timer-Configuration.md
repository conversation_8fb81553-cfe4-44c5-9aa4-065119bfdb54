# 睡眠模式定时器配置说明

## 概述

睡眠模式定时器用于在指定时间段内自动控制设备的指示灯状态，实现睡眠时段的静音显示功能。

## 参数结构

### 定时器主要参数

| 参数名 | 类型 | 范围 | 说明 |
|--------|------|------|------|
| mode | enum | 0-1 | 睡眠模式开关 (0-关闭, 1-开启) |
| num | int | 0-9999999 | 定时器数量 |
| timer | array | ObjectNum:8 | 定时器参数数组 |

### 定时器数组参数

| 参数名 | 类型 | 范围 | 说明 |
|--------|------|------|------|
| id | int | 1-100 | 定时器编号 |
| enable | bool | 0-1 | 定时器开关 (0-关闭, 1-开启) |
| start | string | StringLength:8 | 开始时间 (UTC格式: HHmmssZ, 如121200Z) |
| end | string | StringLength:8 | 结束时间 (UTC格式: HHmmssZ, 如070000Z) |
| week | int | 0-127 | 重复星期设置 (位运算) |
| sid | string | StringLength:256 | 执行服务ID |
| para | string | StringLength:64 | 服务属性名 |
| paraValue | int | -1024-1024 | 服务属性值 |
| sid2 | string | StringLength:256 | 第二个服务ID (可选) |
| para2 | string | StringLength:64 | 第二个服务属性 (可选) |
| paraValue2 | int | -1024-1024 | 第二个服务属性值 (可选) |
| sid3 | string | StringLength:256 | 第三个服务ID (可选) |
| para3 | string | StringLength:64 | 第三个服务属性 (可选) |
| paraValue3 | int | -1024-1024 | 第三个服务属性值 (可选) |

## 周重复设置 (week参数)

week参数使用7位二进制数表示周一到周日的重复设置：

| 数值 | 二进制 | 说明 |
|------|--------|------|
| 0 | 0000000 | 执行一次 |
| 31 | 0011111 | 工作日重复 (周一到周五) |
| 96 | 1100000 | 周末重复 (周六周日) |
| 127 | 1111111 | 每日重复 |

### 自定义重复计算

```javascript
// 例如：周一、周三、周五重复
// 位0=周一, 位2=周三, 位4=周五
let weekValue = 0;
weekValue |= (1 << 0);  // 周一
weekValue |= (1 << 2);  // 周三  
weekValue |= (1 << 4);  // 周五
// 结果: weekValue = 21 (二进制: 0010101)
```

## 时间格式要求

### UTC时间格式 (HHmmssZ)
- **格式**: 24小时制，HHmmssZ格式
- **示例**: 121200Z 表示 12:12:00
- **要求**: 开启时间和关闭时间至少设置一个
- **一次性闹钟**: 当week为0时，作为一次性闹钟处理

### 时间格式转换示例
```javascript
// 22:00 -> 220000Z
// 07:30 -> 073000Z
// 12:12 -> 121200Z
```

## 下发数据示例

### 基本睡眠模式配置

```javascript
const timerData = {
    mode: 1,        // 开启睡眠模式
    num: 1,         // 1个定时器
    timer: [{
        id: 1,                    // 定时器编号
        enable: 1,                // 开启定时器
        start: "220000Z",         // 晚上10点开始 (UTC格式)
        end: "070000Z",           // 早上7点结束 (UTC格式)
        week: 127,                // 每日重复
        sid: "indicator",         // 指示灯服务
        para: "on",               // 开关属性
        paraValue: 0,             // 关闭指示灯
        sid2: "",                 // 未使用
        para2: "",                // 未使用
        paraValue2: 0,            // 未使用
        sid3: "",                 // 未使用
        para3: "",                // 未使用
        paraValue3: 0             // 未使用
    }]
};

// 下发命令
this.$store.dispatch("setDevInfo", { timer: timerData });
```

### 工作日睡眠模式配置

```javascript
const timerData = {
    mode: 1,
    num: 1,
    timer: [{
        id: 1,
        enable: 1,
        start: "230000Z",         // 晚上11点开始 (UTC格式)
        end: "063000Z",           // 早上6点30分结束 (UTC格式)
        week: 31,                 // 仅工作日 (周一到周五)
        sid: "indicator",
        para: "on",
        paraValue: 0,             // 睡眠时关闭指示灯
        sid2: "",
        para2: "",
        paraValue2: 0,
        sid3: "",
        para3: "",
        paraValue3: 0
    }]
};
```

### 一次性闹钟配置

```javascript
const timerData = {
    mode: 1,
    num: 1,
    timer: [{
        id: 1,
        enable: 1,
        start: "220000Z",         // 仅设置开始时间
        end: "",                  // 结束时间可以为空
        week: 0,                  // week为0表示一次性闹钟
        sid: "indicator",
        para: "on",
        paraValue: 0,
        sid2: "",
        para2: "",
        paraValue2: 0,
        sid3: "",
        para3: "",
        paraValue3: 0
    }]
};
```

## 实现要点

1. **UTC时间格式转换**: 界面使用分钟索引，下发时需转换为"HHmmssZ"UTC格式
2. **时间设置要求**: 开启时间和关闭时间至少设置一个，不能都为空
3. **一次性闹钟**: 当week为0时，作为一次性闹钟处理
4. **跨日处理**: 当结束时间早于开始时间时，表示跨日执行
5. **周重复计算**: 使用位运算处理自定义重复设置
6. **参数验证**: 下发前验证所有必需参数的有效性
7. **状态上报**: 参数变化时自动上报
8. **错误处理**: 提供清晰的错误提示和日志记录

## 调试信息

在开发过程中，可以通过控制台查看详细的参数解析和下发信息：

```javascript
console.log('睡眠模式定时器下发数据:', timerData);
console.log('Timer数据解析:', {
    原始数据: timerItem,
    开始时间索引: this.startIndex,
    结束时间索引: this.endIndex,
    周重复值: week
});
```

## 注意事项

1. 定时器ID必须在1-100范围内
2. 时间字符串必须为UTC格式"HHmmssZ"，如"121200Z"
3. 开启时间和关闭时间至少设置一个，不能都为空字符串
4. week参数范围为0-127，0表示一次性闹钟，其他值表示重复闹钟
5. 服务ID和属性名不能为空字符串（除非是可选参数）
6. 属性值必须在-1024到1024范围内
7. 参数变化时会自动触发上报机制
8. UTC时间格式中的秒数部分固定为00
