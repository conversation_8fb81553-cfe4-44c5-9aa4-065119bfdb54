<template>
    <div class="custom-calendar">
        <!-- 星期标题 -->
        <div class="calendar-weekdays">
            <div 
                v-for="weekday in weekdays" 
                :key="weekday" 
                class="weekday-item"
            >
                {{ weekday }}
            </div>
        </div>

        <!-- 日历网格 -->
        <div class="calendar-grid">
            <div
                v-for="(day, index) in calendarDays"
                :key="`${day.year}-${day.month}-${day.date}-${index}`"
                :class="getDayClasses(day)"
                @click="onDayClick(day)"
            >
                <span class="day-text">{{ day.date }}</span>
                <div v-if="day.isToday" class="today-indicator"></div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    getStartOfWeek,
    getEndOfWeek,
    isSameDay as utilIsSameDay,
    isSameWeek,
    getWeekRange,
    isDateInRange as utilIsDateInRange,
    resetTime
} from '../utils/calendar.js';

export default {
    name: 'CustomCalendar',
    props: {
        // 选择模式：'single' | 'range' | 'week'
        mode: {
            type: String,
            default: 'single',
            validator: (value) => ['single', 'range', 'week'].includes(value)
        },
        // 当前显示的年月
        currentDate: {
            type: Date,
            default: () => new Date()
        },
        // 选中的日期（单选模式）
        selectedDate: {
            type: Date,
            default: null
        },
        // 选中的日期范围（范围选择模式）
        selectedRange: {
            type: Array,
            default: () => []
        },
        // 最小日期
        minDate: {
            type: Date,
            default: null
        },
        // 最大日期
        maxDate: {
            type: Date,
            default: null
        }
    },
    data() {
        return {
            weekdays: ['一', '二', '三', '四', '五', '六', '日'],
            calendarDays: []
        };
    },
    watch: {
        currentDate: {
            handler() {
                this.generateCalendarDays();
            },
            immediate: true
        },
        selectedDate: {
            handler() {
                this.generateCalendarDays();
            }
        },
        selectedRange: {
            handler() {
                this.generateCalendarDays();
            },
            deep: true
        }
    },
    methods: {
        // 生成日历天数数据
        generateCalendarDays() {
            const year = this.currentDate.getFullYear();
            const month = this.currentDate.getMonth();
            
            // 获取当月第一天
            const firstDay = new Date(year, month, 1);
            // 获取当月最后一天
            const lastDay = new Date(year, month + 1, 0);
            
            // 获取第一天是星期几（0=周日，1=周一...）
            let firstDayWeek = firstDay.getDay();
            // 转换为周一开始（0=周一，1=周二...）
            firstDayWeek = firstDayWeek === 0 ? 6 : firstDayWeek - 1;
            
            const days = [];
            
            // 添加上个月的日期（填充第一周）
            // 正确计算上个月的年份和月份
            let prevYear = year;
            let prevMonth = month - 1;
            if (prevMonth < 0) {
                prevMonth = 11;
                prevYear = year - 1;
            }

            // 获取上个月的最后一天
            const prevMonthLastDay = new Date(year, month, 0).getDate();

            for (let i = firstDayWeek - 1; i >= 0; i--) {
                const date = prevMonthLastDay - i;
                days.push(this.createDayObject(prevYear, prevMonth, date, false));
            }
            
            // 添加当月的日期
            for (let date = 1; date <= lastDay.getDate(); date++) {
                days.push(this.createDayObject(year, month, date, true));
            }
            
            // 添加下个月的日期（填充最后一周）
            const remainingDays = 42 - days.length; // 6周 × 7天 = 42天
            for (let date = 1; date <= remainingDays; date++) {
                const nextMonth = new Date(year, month + 1, 1);
                days.push(this.createDayObject(nextMonth.getFullYear(), nextMonth.getMonth(), date, false));
            }
            
            this.calendarDays = days;
        },
        
        // 创建日期对象
        createDayObject(year, month, date, isCurrentMonth) {
            const dayDate = new Date(year, month, date);
            const today = new Date();
            
            return {
                year,
                month,
                date,
                fullDate: dayDate,
                isCurrentMonth,
                isToday: this.isSameDay(dayDate, today),
                isSelected: this.isDateSelected(dayDate),
                isInRange: this.isDateInRange(dayDate),
                isRangeStart: this.isRangeStart(dayDate),
                isRangeEnd: this.isRangeEnd(dayDate),
                isDisabled: this.isDateDisabled(dayDate)
            };
        },
        
        // 判断是否是同一天
        isSameDay(date1, date2) {
            return utilIsSameDay(date1, date2);
        },
        
        // 判断日期是否被选中
        isDateSelected(date) {
            if (this.mode === 'single') {
                return this.selectedDate && this.isSameDay(date, this.selectedDate);
            } else if (this.mode === 'week') {
                return this.selectedRange.length === 2 && this.isDateInRange(date);
            }
            return false;
        },
        
        // 判断日期是否在选中范围内
        isDateInRange(date) {
            if ((this.mode === 'range' || this.mode === 'week') && this.selectedRange.length === 2) {
                const [start, end] = this.selectedRange;
                return date >= start && date <= end;
            }
            return false;
        },
        
        // 判断是否是范围开始
        isRangeStart(date) {
            if (this.mode === 'range' && this.selectedRange.length >= 1) {
                return this.isSameDay(date, this.selectedRange[0]);
            }
            return false;
        },
        
        // 判断是否是范围结束
        isRangeEnd(date) {
            if (this.mode === 'range' && this.selectedRange.length === 2) {
                return this.isSameDay(date, this.selectedRange[1]);
            }
            return false;
        },
        
        // 判断日期是否被禁用
        isDateDisabled(date) {
            return !utilIsDateInRange(date, this.minDate, this.maxDate);
        },
        
        // 获取日期的CSS类
        getDayClasses(day) {
            return {
                'calendar-day': true,
                'current-month': day.isCurrentMonth,
                'other-month': !day.isCurrentMonth,
                'today': day.isToday,
                'selected': day.isSelected,
                'in-range': day.isInRange,
                'range-start': day.isRangeStart,
                'range-end': day.isRangeEnd,
                'disabled': day.isDisabled
            };
        },
        
        // 处理日期点击
        onDayClick(day) {
            if (day.isDisabled) return;

            if (this.mode === 'single') {
                this.$emit('date-select', day.fullDate);
            } else if (this.mode === 'range') {
                this.handleRangeSelect(day.fullDate);
            } else if (this.mode === 'week') {
                this.handleWeekSelect(day.fullDate);
            }
        },
        
        // 处理范围选择
        handleRangeSelect(date) {
            if (this.selectedRange.length === 0) {
                // 选择开始日期
                this.$emit('range-select', [date]);
            } else if (this.selectedRange.length === 1) {
                // 选择结束日期
                const start = this.selectedRange[0];
                if (date < start) {
                    // 如果选择的日期早于开始日期，重新开始选择
                    this.$emit('range-select', [date]);
                } else {
                    // 完成范围选择
                    this.$emit('range-select', [start, date]);
                }
            } else {
                // 重新开始选择
                this.$emit('range-select', [date]);
            }
        },

        // 处理周选择
        handleWeekSelect(date) {
            const weekRange = getWeekRange(date);
            this.$emit('week-select', weekRange);
        }
    }
};
</script>

<style lang="less" scoped>
.custom-calendar {
    width: 100%;
    user-select: none;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    padding: 0 2.4rem 0.8rem 2.4rem;
    background-color: transparent;
    
    .weekday-item {
        text-align: center;
        font-size: 1.4rem;
        color: var(--emui_text_primary);
        padding: 0.8rem 0;
        font-weight: 500;
    }
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    padding: 0 2.4rem;
    gap: 0;
}

.calendar-day {
    position: relative;
    height: 4.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    
    .day-text {
        font-size: 1.6rem;
        font-weight: 500;
        color: var(--emui_text_primary);
        position: relative;
        z-index: 2;
    }
    
    .today-indicator {
        position: absolute;
        bottom: 0.4rem;
        left: 50%;
        transform: translateX(-50%);
        width: 0.4rem;
        height: 0.4rem;
        background-color: var(--emui_accent);
        border-radius: 50%;
        z-index: 1;
    }
    
    &.other-month .day-text {
        color: var(--emui_text_secondary);
    }
    
    &.disabled {
        cursor: not-allowed;
        
        .day-text {
            color: var(--emui_text_secondary);
            opacity: 0.5;
        }
    }
    
    &.selected {
        .day-text {
            background-color: var(--emui_accent);
            color: white;
            border-radius: 50%;
            width: 3.2rem;
            height: 3.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .today-indicator {
            display: none;
        }
    }
    
    &.in-range {
        background-color: rgba(var(--emui_accent_rgb), 0.1);
        
        .day-text {
            color: var(--emui_accent);
        }
    }
    
    &.range-start,
    &.range-end {
        .day-text {
            background-color: var(--emui_accent);
            color: white;
            border-radius: 50%;
            width: 3.2rem;
            height: 3.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .today-indicator {
            display: none;
        }
    }
    
    &:hover:not(.disabled):not(.selected) {
        .day-text {
            background-color: rgba(var(--emui_accent_rgb), 0.1);
            border-radius: 50%;
            width: 3.2rem;
            height: 3.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
