<!--
 * @Author: y<PERSON><PERSON><PERSON><PERSON>@talkweb.com.cn
 * @Date: 2025-06-16 10:44:37
 * @LastEditTime: 2025-06-17 15:24:58
 * @LastEditors: <EMAIL>
-->
<template>
    <div class="dialog">
        <div class="dialog-container">
            <div class="mod-head">
                <span
                    id="com.huawei.smarthome:id/dialog_radio_title"
                    class="title"
                    >{{ title }}</span
                >
                <span class="subtitle" v-if="subtitle">{{ subtitle }}</span>
            </div>
            <radio
                :options="values"
                @change="onRadioChange"
                :value="defaultIndex"
            ></radio>
            <div class="dialog-btns">
                <div
                    id="com.huawei.smarthome:id/dialog_radio_cancel"
                    @click.stop="cancel(true)"
                >
                    <p>{{ $t("cancel") }}</p>
                </div>
                <span v-if="showOk == true" class="line"></span>
                <div
                    v-if="showOk == true"
                    id="com.huawei.smarthome:id/dialog_radio_confirm"
                    @click.stop="confirm"
                >
                    <p>{{ $t("ok") }}</p>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import radio from "./radio/radio";

export default {
    props: {
        title: {
            default: "",
            type: String,
        },
        subtitle: {
            default: null,
            type: String,
        },
        values: {
            default: Array(),
            type: Array,
        },
        defaultIndex: {
            default: 0,
            type: [Number, String],
        },
        showOk: {
            default: true,
            type: Boolean,
        },
    },
    name: "DialogRadioPicker",
    components: { radio },
    data() {
        return {
            selectedValue: this.defaultIndex,
        };
    },
    computed: {},
    methods: {
        cancel(check) {
            this.$emit("cancel", check);
        },
        confirm() {
            this.$emit("confirm", this.selectedValue);
        },
        onRadioChange(value) {
            this.selectedValue = value;
            if (!this.showOk) {
                this.$emit("confirm", this.selectedValue);
                this.$emit("cancel", false);
            }
        },
    },
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.red {
    color: var(--emui_functional_red);
}

.picker {
    margin: 0.8rem 2.4rem 0px 2.4rem;
}
</style>
