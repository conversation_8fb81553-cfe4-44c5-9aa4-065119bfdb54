@import url("../../lib/style/public.less");

#service-hotline-page {
    overflow-y: auto;
    background-color: var(--emui_color_subbg);

    .content {
        margin: 6.6rem 0.6rem 0px 0.6rem;
        position: relative;
        padding-bottom: 2.4rem;
    }

    .module-box {
        .cardStyle();
        margin-bottom: 1.2rem;
    }

    .service-hotline-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        min-height: 4.8rem;
        cursor: pointer;
        padding: 0.8rem 0;

        .left {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.4rem;

            .phone-number {
                font-size: 1.4em;
                color: var(--emui_text_secondary);
            }

            .working-hours {
                font-size: 1.2rem;
                color: var(--emui_text_secondary);
                line-height: 1.4;
            }
        }

        .right {
            display: flex;
            align-items: center;

            .icon {
                width: 2.4rem;
                height: 2.4rem;
                object-fit: contain;
            }
        }
    }
}

// 决策理由：适配平板设备的边距
.pad #service-hotline-page {
    .content {
        margin: 6.6rem var(--home_margin) 0px var(--home_margin);
    }
}
