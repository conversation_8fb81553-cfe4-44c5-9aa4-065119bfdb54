<template>
  <div id="environment-overview"  @click="handleClick">
    <div class="box">
      <!-- 环境质量背景图片 -->
      <img class="environment-bg-image"
           :src="qualityLevel == 1 ? require('../assets/enviroment_overview_excellent.png') : qualityLevel == 2 ? require('../assets/enviroment_overview_good.png') : require('../assets/enviroment_overview_low.png')"
           alt="环境质量背景" />

      <div class="left">
        <div class="quality-gauge">
          <GaugeChart
            :width="320"
            :height="300"
            :value="qualityLevel"
            :disabled="disabled"
          />
        </div>
      </div>

      <div class="right">
        <div class="environment-info">
          <div class="environment-title">{{ $t('current_indoor_environment') }}</div>
          <div class="environment-subtitle">{{ $t('comprehensive_data_reference') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GaugeChart from './GaugeChart.vue';

export default {
  name: "EnvironmentOverview",
  components: {
    GaugeChart
  },
  props: {
    co2Current: {
      default: 0,
      type: Number
    },
    co2Level: {
      default: 1,
      type: Number
    },
    hchoCurrent: {
      default: 0,
      type: Number
    },
    temperature: {
      default: 0,
      type: Number
    },
    humidity: {
      default: 0,
      type: Number
    },
    disabled: {
      default: false,
      type: Boolean
    }
  },
  computed: {
    // 计算综合环境质量等级
    qualityLevel() {
      console.log("co2Current", this.co2Current)
      console.log("hchoCurrent", this.hchoCurrent)
      if (this.disabled) return 0;

      // 首先检查是否为"差"（任一指标超标）
      if (this.co2Current > 1000 || this.hchoCurrent > 0.08) {
        return 3; // 差
      }

      // 检查是否为"优"（两个指标都在优秀范围）
      if (this.co2Current <= 450 && this.hchoCurrent <= 0.05) {
        return 1; // 优
      }

      // 检查是否为"良"（两个指标都在良好范围）
      if ((this.co2Current >= 450 && this.co2Current <= 1000) ||
          (this.hchoCurrent > 0.05 && this.hchoCurrent <= 0.08)) {
        return 2; // 良
      }

      // 其他情况（指标不在同一等级范围内）默认为"差"
      return 3; // 差
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
};
</script>

<style lang='less' scoped>
@import url("../../lib/style/public.less");

#environment-overview {
  width: 100%;
  color: var(--emui_text_primary);

  .box {
    position: relative;
    height: 11rem;
    margin: 0 var(--emui_dimens_default_start);
    display: flex;
    align-items: center;
    .cardStyle();
    cursor: pointer;
    padding: 0.8rem 1.2rem;

    .environment-bg-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover; /* 保持图片比例并填充容器 */
      pointer-events: none; /* 确保不影响点击事件 */
      z-index: 1; /* 在白色背景上面 */
    }

    .left {
      position: relative;
      z-index: 2; /* 在背景图片上面 */
      display: flex;
      align-items: center;
      margin-right: 2.4rem;

      .quality-gauge {
        width: 12rem;
        height: 11rem;
      }
    }

    .right {
      position: relative;
      z-index: 2; /* 在背景图片上面 */
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .environment-info {
        .environment-title {
          font-size: 1.8rem;
          font-weight: 500;
          color: var(--emui_text_primary);
          margin-bottom: 0.8rem;
          line-height: 1.2;
        }

        .environment-subtitle {
          font-size: 1.2rem;
          color: var(--emui_text_secondary);
          line-height: 1.3;
        }
      }
    }
  }

  &.disabled .box {
    opacity: 0.4;
    cursor: default;
  }
}

.pad #environment-overview .box {
  padding: 0px;
}

.tahiti #environment-overview .box {
  padding: 0px;
}
</style>
