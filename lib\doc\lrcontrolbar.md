# LRControlBar

### 一般用于3等份UI 用于左右都有控制按钮的

| prop参数 | 描述 |
| --- | --- |
| value      | 中间控制器显示值|
| desc         | 控制器描述|

### 1.需要添加左右插槽

#### ![](./images/lrcontroll_bar.png)

```` JavaScript
 <LRControlBar :value="temperatureText" :desc="$t('rfwd')" :disabled="!(canControl && Switch == 1)">
    <div slot="left" class="lrimg imgreduce" @click="onTemperatureClick(-1)" :class="{'disabled': fomentTemperature === 0}"></div>
    <div slot="right" class="lrimg imgadd" @click="onTemperatureClick(1)" :class="{'disabled': fomentTemperature === 9}"></div>
  </LRControlBar>
````
