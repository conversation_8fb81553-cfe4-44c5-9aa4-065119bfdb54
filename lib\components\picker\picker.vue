<template>
    <div class="picker" :class="{ 'picker-3d': rotateEffect }">
        <div class="picker-toolbar" v-if="showToolbar">
            <slot></slot>
        </div>
        <div class="picker-items">
            <picker-slot v-for="(slot, index) in slots" :valueKey="valueKey" :values="slot.values || []"
                         :text-align="slot.textAlign || 'center'" :visible-item-count="visibleItemCount"
                         :class-name="slot.className" :flex="slot.flex" v-model="values[slot.valueIndex]"
                         :rotate-effect="rotateEffect" :divider="slot.divider" :content="slot.content"
                         :itemHeight="itemHeight" :default-index="slot.defaultIndex" :unit="unit ? unit : slot.unit" :centerHeight="centerHeight" :key="index"></picker-slot>
            <div class="picker-center-highlight"
                 :style="{ height: centerHeight + 'px', marginTop: -centerHeight / 2 + 'px' }"></div>
            <div class="upfilter"></div>
            <div class="downfilter"></div>
        </div>
    </div>
</template>

<style>
.picker {
    overflow: hidden;
}

.picker-toolbar {
    height: 4.0rem;
}

.picker-items {
    display: flex;
    justify-content: center;
    padding: 0;
    text-align: right;
    font-size: 2.4rem;
    position: relative;
}

.picker-center-highlight {
    box-sizing: border-box;
    position: absolute;
    left: 0;
    width: 100%;
    top: 50%;
    margin-top: -1.8rem;
    pointer-events: none
}

.picker-center-highlight:before,
.picker-center-highlight:after {
    content: '';
    position: absolute;
    height: 1px;
    width: 100%;
    background-color: var(--emui_color_list_divider);
    display: block;
    z-index: 15;
    transform: scaleY(0.25);
}

.picker-center-highlight:before {
    left: 0;
    top: 0;
    bottom: auto;
    right: auto;
}

.picker-center-highlight:after {
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
}
.picker-items .upfilter {
    left: 0;
    top: 0rem;
    height: 4.2rem;
    position: absolute;
    width: 100%;
    pointer-events: none;
    z-index: 3;
    background: var(--picker-upfilter-bg);
}

.picker-items .downfilter {
    left: 0;
    height: 4.2rem;
    position: absolute;
    z-index: 3;
    width: 100%;
    pointer-events: none;
    bottom: 0rem;
    background: var(--picker-downfilter-bg);
}

</style>

<script type="text/babel">
import PickerSlot from './picker-slot.vue'
export default {
    name: 'picker',
    componentName: 'picker',
    components: {PickerSlot},

    props: {
        slots: {
            type: Array
        },
        showToolbar: {
            type: Boolean,
            default: false
        },
        visibleItemCount: {
            type: Number,
            default: 5
        },
        valueKey: String,
        rotateEffect: {
            type: Boolean,
            default: false
        },
        itemHeight: {
            type: Number,
            default: 36
        },
        centerHeight: {
            type: Number,
            default: 36
        },
        unit: {
            type: String,
            default: null
        }
    },

    created() {
        this.$on('slotValueChange', this.slotValueChange);
        this.slotValueChange();
    },
    methods: {
        slotValueChange() {
            this.$emit('change', this, this.values);
        },

        getSlot(slotIndex) {
            var slots = this.slots || [];
            var count = 0;
            var target;
            var children = this.$children.filter(child => child.$options.name === 'picker-slot');

            slots.forEach(function (slot, index) {
                if (!slot.divider) {
                    if (slotIndex === count) {
                        target = children[index];
                    }
                    count++;
                }
            });

            return target;
        },
        getValues() {
            return this.values;
        }
    },

    computed: {
        values: {
            get() {
                var slots = this.slots || [];
                var values = [];
                var valueIndexCount = 0;
                slots.forEach(slot => {
                    if (!slot.divider) {
                        slot.valueIndex = valueIndexCount++;
                        values[slot.valueIndex] = (slot.values || [])[slot.defaultIndex || 0];
                    }
                });
                return values;
            }
        },
        slotCount() {
            var slots = this.slots || [];
            var result = 0;
            slots.forEach(function (slot) {
                if (!slot.divider) result++;
            });
            return result;
        }
    },

};
</script>
