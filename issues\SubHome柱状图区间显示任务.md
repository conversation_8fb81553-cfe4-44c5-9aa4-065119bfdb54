# SubHome柱状图区间显示任务

## 任务背景
用户要求将SubHome页面的柱状图改为显示最大值最小值的区间图表，每个时间点都有最大值和最小值，柱状图显示为中间区域表示数值范围。

## 需求确认
1. 数据结构改为：`[{min: 300, max: 500}, {min: 320, max: 480}, ...]`
2. 柱状图显示为从最小值到最大值的垂直条形区间
3. 不需要在柱状图上显示具体数字
4. 适用于所有传感器类型（CO2、甲醛、温度、湿度）

## 执行计划
### 第一步：修改数据聚合逻辑（SubHome.vue）
- [x] 修改 `aggregateDataByHours()` 方法计算最大值最小值
- [x] 修改 `aggregateDataByDays()` 方法计算最大值最小值
- [x] 修改 `aggregateDataByMonthIntervals()` 方法计算最大值最小值

### 第二步：修改LineChart组件支持区间数据（LineChart.vue）
- [x] 修改 `chartData` computed属性兼容新数据格式
- [x] 修改 `renderChart()` 方法的ECharts配置
- [x] 修改静态数据为{min, max}格式

### 第三步：修改点击事件和数值显示
- [x] 修改点击事件处理传递min和max值
- [x] 修改SubHome中的数值显示逻辑

### 第四步：UI优化和修复
- [x] 修改数值显示：日报显示值，周报月报显示范围
- [x] 给柱状图添加圆角（四个角都有圆角）
- [x] 修复Y轴值被覆盖问题（增加right边距）
- [x] 添加选中柱状图的垂直直线

### 第五步：测试和优化
- [ ] 测试所有传感器类型和报表类型
- [ ] 验证区间柱状图视觉效果
- [ ] 确保点击交互正常工作

## 技术实现
- 使用ECharts自定义series配置显示区间柱状图
- 保持向后兼容，支持原有简单数组格式
- 数据格式：`[{min: number, max: number}, ...]`
