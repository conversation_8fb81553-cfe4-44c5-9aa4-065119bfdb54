<template>
  <div class="product-card" @click="handleClick">
    <!-- 背景图片 -->
    <img class="product-bg-image" 
         :src="require('../assets/product_wiki.png')"
         alt="产品百科背景" />
    
    <!-- 卡片内容 -->
    <div class="product-content">
      <div class="product-text">
        <div class="product-title">{{ mainText }}</div>
        <div class="product-subtitle" v-if="subText">{{ subText }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProductCard",
  props: {
    // 主文本（卡片标题）
    mainText: {
      type: String,
      default: '产品百科'
    },
    // 副文本（卡片副标题）
    subText: {
      type: String,
      default: '使用指南 | 功能介绍 | 常见问题'
    }
  },
  methods: {
    handleClick() {
      // 只触发点击事件，不做跳转
      this.$emit('click');
    }
  }
};
</script>

<style lang='less' scoped>
@import url("../../lib/style/public.less");

.product-card {
  position: relative;
  width: 100%;
  height: 11.2rem; // 336×112vp 转换为 rem (112/10 = 11.2rem)
  margin: 0 var(--emui_dimens_default_start);
  .cardStyle();
  cursor: pointer;
  overflow: hidden;
  
  &:active {
    opacity: 0.8;
  }

  .product-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: fill; /* 保持宽高比，裁剪多余部分 */
    object-position: center; /* 默认居中显示 */
    pointer-events: none;
    z-index: 1;
  }

  .product-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    // padding: 1.6rem 2.4rem;
    box-sizing: border-box;

    .product-text {
      flex: 1;

      .product-title {
        font-size: 2.0rem; // 20sp 转换为 rem (20/10 = 2.0rem)
        color: #FFFFFF;
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: 0.4rem;
        
        // 文字投影：X偏移1px，Y偏移1px，模糊值2px，投影颜色#02375B，透明度50%
        text-shadow: 0.1rem 0.1rem 0.2rem rgba(2, 55, 91, 0.5);
        
        // 文本超长处理：逐级缩小字号，最小14sp，不换行，超长截断
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        
        // 响应式字体大小
        @media (max-width: 480px) {
          font-size: 1.8rem; // 18sp
        }
        
        @media (max-width: 360px) {
          font-size: 1.6rem; // 16sp
        }
        
        @media (max-width: 320px) {
          font-size: 1.4rem; // 14sp (最小字号)
        }
      }

      .product-subtitle {
        font-size: 1.0rem; // 10sp 转换为 rem (10/10 = 1.0rem)
        color: #FFFFFF;
        line-height: 1.3;
        
        // 文字投影：X偏移1px，Y偏移1px，模糊值2px，投影颜色#02375B，透明度50%
        text-shadow: 0.1rem 0.1rem 0.2rem rgba(2, 55, 91, 0.5);
        
        // 副文本处理：支持换行（最多一行），超长截断
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 深色模式适配
#app.dark .product-card {
  .product-content {
    .product-text {
      .product-title,
      .product-subtitle {
        // 在深色模式下保持白色文字，因为背景图片通常是深色的
        color: #FFFFFF;
      }
    }
  }
}

// 手机端图片显示优化：保留左右两侧内容，裁剪中间
// 默认手机端样式（非.pad类时）
.product-card {
  .product-bg-image {
    object-position: center center; /* 居中显示，自然保留左右两侧 */
  }
}

// 平板适配（折叠屏展开/pad竖屏时）
.pad .product-card {
  height: 14.0rem; // 平板上稍微增加高度

  .product-bg-image {
    object-position: center; /* 平板保持居中显示 */
  }

  .product-content {
    padding: 2.0rem 3.0rem;

    .product-text {
      .product-title {
        font-size: 2.4rem; // 平板上增大字体
      }

      .product-subtitle {
        font-size: 1.2rem; // 平板上增大字体
      }
    }
  }
}
</style>
