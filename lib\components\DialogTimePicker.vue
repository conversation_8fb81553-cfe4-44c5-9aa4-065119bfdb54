<template>
  <div class="dialog" @click.stop="cancel(false)">
    <div class="dialog-container">
      <div class="mod-head">
        <span id="com.huawei.smarthome:id/dialog_time_title" class="title">{{ title }}</span>
        <span class="subtitle" v-if="subtitle">{{subtitle}}</span>
      </div>
      <picker ref="picker" class="picker" :slots="slots" :centerHeight='centerHeight' :itemHeight="itemHeight"
        @change="onValuesChange" :rotateEffect="true"></picker>
      <div class="dialog-btns">
        <div id="com.huawei.smarthome:id/dialog_time_cancel" @click.stop='cancel(true)'>
          <p :class="{ 'red': leftBtnRed }">{{ leftBtnText || $t('cancel') }}</p>
        </div>
        <span class="line"></span>
        <div id="com.huawei.smarthome:id/dialog_time_confirm" @click.stop='confirm'>
          <p>{{ $t('ok') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import picker from './picker/picker';

export default {
  props: {
    title: {
      default: '',
      type: String
    },
    subtitle: {
      default: null,
      type: String
    },
    startTime: {
      default: 1,
      type: Number
    },
    limitLength: {
      default: 1439,
      type: Number
    },
    leftBtnText: {
      default: null,
      type: String
    },
    leftBtnRed: {
      default: false,
      type: Boolean
    },
    defaultValue: {
      default: null,
      type: Number
    },
    countDown: {
      default: true,
      type: Boolean
    }
  },
  name: 'DialogTimePicker',
  components: { picker },
  data() {
    return {
      flagHourIndex: '',
      timeData: [
        {values: [], defaultIndex : 0},
        {values: [], defaultIndex : 0}
      ]
    };
  },
  mounted() {
    this.doOnValuesChange()
  },
  computed: {
    centerHeight() {
      let fontSize = parseFloat(document.querySelector('html')
        .style
        .fontSize
        .replace('px', ''));
      return 56 * (fontSize / 10);
    },
    itemHeight() {
      let fontSize = parseFloat(document.querySelector('html')
        .style
        .fontSize
        .replace('px', ''));
      return 36 * (fontSize / 10);
    },
    slots() {
      return [
        {
          flex: 1,
          values: this.timeData[0].values,
          className: 'slot1',
          textAlign: 'center',
          defaultIndex: this.timeData[0].defaultIndex,
          unit: this.$t(this.countDown ? 'time_unit_hour2' : 'time_unit_hour')
        },
        {
          flex: 1,
          values: this.timeData[1].values,
          className: 'slot2',
          textAlign: 'center',
          defaultIndex: this.timeData[1].defaultIndex,
          unit: this.$t(this.countDown ? 'time_unit_minute2' : 'time_unit_minute')
        }
      ];
    }
  },
  methods: {
    cancel(check) {
      this.$emit('cancel', check);
    },
    confirm() {
      // this.$emit('input', parseInt(this.curValue[0]) * 60 +  parseInt(this.curValue[1]));
      this.$emit('confirm', this.curValue);
    },
    doOnValuesChange() {
      const hourValues = [];
      var startHour = parseInt(this.startTime / 60);
      var endHour = parseInt((this.startTime + this.limitLength) / 60);
      for (let i = startHour; i <= endHour; i++) {
        if (this.countDown) {
          hourValues.push(i);
        } else {
          hourValues.push(i < 24 ? i : (i - 24));
        }
      }
      this.timeData[0].values = hourValues
      if (this.defaultValue) {
        this.timeData[0].defaultIndex = parseInt((this.defaultValue - this.timeData[0].values[0] * 60) / 60);
        if (this.timeData[0].defaultIndex >= hourValues.length) {
          this.timeData[0].defaultIndex = 0;
        }
        this.flagHourIndex = this.timeData[0].defaultIndex;
        const minute = this.defaultValue - this.timeData[0].values[this.flagHourIndex] * 60;
        this.onValuesChange(null,[hourValues[this.flagHourIndex], minute >= 10 ? minute : `0${minute}`]);
        if (this.timeData[1].values) {
          let index =  this.timeData[1].values.findIndex(item => item == (minute >= 10 ? minute : `0${minute}`))
          this.timeData[1].defaultIndex = index <= 0 ? 0 : index
        }
      } else {
        this.timeData[0].defaultIndex = 0;
        this.flagHourIndex = this.timeData[0].defaultIndex;
        if (this.timeData[1].values) {
          this.timeData[1].defaultIndex = 0
        }
        this.onValuesChange( null, [hourValues[this.flagHourIndex], '00']);
      }
    },
    onValuesChange(picker, values) {
      let arr = []
      const newHourIndex = this.timeData[0].values.findIndex(item => item === values[0])
      if (picker == null || this.flagHourIndex != newHourIndex) {
        if (newHourIndex === this.timeData[0].values.length - 1) {  //结束
          var endTime = (this.startTime + this.limitLength) % 60;
          for (let i = 0; i <= endTime; i++) {
            arr.push(i < 10 ? '0' + i : i)
          }
          this.timeData[1].values = arr
          if(picker){
            this.timeData[1].defaultIndex = 0
          }
        } else if (newHourIndex === 0) { //起始
          var startTime = this.startTime % 60;
          for (let i = startTime; i <= 59; i++) {
            arr.push(i < 10 ? '0' + i : i);
          }
          this.timeData[1].values = arr;
          if(picker){
            this.timeData[1].defaultIndex = 0;
          }
        } else { //中间
          if (picker == null || this.flagHourIndex === this.timeData[0].values.length - 1 || this.flagHourIndex === 0) {
            for (let i = 0; i <= 59; i++) {
              arr.push(i < 10 ? '0' + i : i);
            }
            this.timeData[1].values = arr;
            if(picker){
              this.timeData[1].defaultIndex = 0;
            }
          }
        }
        this.flagHourIndex = newHourIndex;
        this.timeData[0].defaultIndex = this.flagHourIndex
      }
      this.curValue = values
    }
  },
  watch: {
    startTime(val) {
      this.doOnValuesChange()
    },
    limitLength(val) {
      this.doOnValuesChange()
    },
    defaultValue(val) {
      this.doOnValuesChange()
    }
  }
};
</script>

<style lang="less" scoped>
@import url("./../style/public.less");

.red {
  color: var(--emui_functional_red);
}

.picker {
  margin: 0.8rem 2.4rem 0px 2.4rem;
}
</style>
