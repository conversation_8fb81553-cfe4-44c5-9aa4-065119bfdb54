# ModeBar

#### 一般用于一排模式按钮选择组件

| prop参数 | 描述 |
| --- | --- |
| disabled      |是否禁用, Bool|
| modes         | 模式的列表数据 Array 数据库包含name 模式名称，value：模式值， img:模式图片（只需要图片前缀，图片放置在assets/目录下 图片名称结尾是 _on.png(开启), _off.png（关闭）。 如果有spinner切换需要添加spinner数据|
| mode         |当前选择的模式value, Number|

| event | 描述 |
| --- | --- |
| switchMode      |点击切换模式事件|
| spinnerClicked      |如果有下拉框选择切换 则选中下拉框点击事件|

### 1.普通模式切换

#### ![](./images/mode_normal.png)

```` JavaScript
modes: [
  {name: this.$t('mode_0'), value: 0, img: 'ic_mode0'},
  {name: this.$t('mode_1'), value: 1, img: 'ic_mode1'},
  {name: this.$t('mode_2'), value: 2, img: 'ic_mode2'}
]
<ModeBar :modes="modes" :mode="Mode"  :disabled="!(canControl && Switch == 1)" @switchMode="switchMode"/>
````

### 2.带有spinner的模式切换

#### 在mode的数据item需要添加spinner数据,spinner包含数据项items和value当前选择的值

#### ![](./images/mode_spinner.png)

```` JavaScript
modes: [
  {name: this.$t('mode_0'), value: 0, img: 'ic_mode0',
    spinner: {
      items: [
        {name: ('1' + this.$t('mode_unit')), value: 0},
        {name: ('2' + this.$t('mode_unit')), value: 1}
      ],
      value: -1,
    }
  },
  {name: this.$t('mode_0'), value: 0, img: 'ic_mode0',
    spinner: {
      items: [
        {name: ('1' + this.$t('mode_unit')), value: 0},
        {name: ('2' + this.$t('mode_unit')), value: 1}
      ],
      value: -1,
    }
  },
  {name: this.$t('mode_0'), value: 0, img: 'ic_mode0',
    spinner: {
      items: [
        {name: ('1' + this.$t('mode_unit')), value: 0},
        {name: ('2' + this.$t('mode_unit')), value: 1}
      ],
      value: -1,
    }
  }
};

 <ModeBar :modes="modes" :mode="Mode" :disabled="!canControl" @spinnerClicked="onStallConfirm"> </ModeBar>
````
