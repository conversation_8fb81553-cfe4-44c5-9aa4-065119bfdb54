import Vue from 'vue';
import Router from 'vue-router';
const Name = name => () => import(`@/views/${name}`);
Vue.use(Router);
Router.prototype.goBack = function (url) {
    this.isBack = true;
    url ? this.push(url) : this.go(-1);
};
export default new Router({
    routes: [
        {
            path: '/',
            redirect: '/Home'
        },
        {
            path: '/Home',
            name: 'Home',
            component: Name('Home')
        },
        {
            path: '/SubHome',
            name: 'SubHome',
            component: Name('SubHome')
        },
        {
            path: '/SleepPage',
            name: 'SleepPage',
            component: Name('SleepPage')
        },
        {
            path: '/ProductWiki',
            name: 'ProductWiki',
            component: Name('ProductWiki')
        },
        {
            path: '/AfterSalesService',
            name: 'AfterSalesService',
            component: Name('AfterSalesService')
        },
        {
            path: '/ServiceHotline',
            name: 'ServiceHotline',
            component: Name('ServiceHotline')
        },
        {
            path: '/ServiceAccount',
            name: 'ServiceAccount',
            component: Name('ServiceAccount')
        },
        {
            path: '/WikiQuickStart',
            name: 'WikiQuickStart',
            component: Name('WikiQuickStart')
        },
        {
            path: '/WikiUsageTips',
            name: 'WikiUsageTips',
            component: Name('WikiUsageTips')
        },
        {
            path: '/WikiAirQuality',
            name: 'WikiAirQuality',
            component: Name('WikiAirQuality')
        },
        {
            path: '/WikiTroubleshooting',
            name: 'WikiTroubleshooting',
            component: Name('WikiTroubleshooting')
        },
        {
            path: '/WikiFAQ',
            name: 'WikiFAQ',
            component: Name('WikiFAQ')
        }
    ]
});