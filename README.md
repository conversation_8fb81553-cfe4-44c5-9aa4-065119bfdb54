# Wi-Fi Demo

> A Vue.js project.
>
> 此为demo代码，不可直接用于商用。

## 使用方法

``` bash
# 安装依赖
npm install

# 运行服务
npm run dev

# 打包服务
npm run build
```

## 工程目录
```shell
│  .babelrc
│  .editorconfig
│  .eslintignore
│  .eslintrc.js							# Eslint配置
│  .gitignore
│  .postcssrc.js
│  index.html							# 主页
│  package-lock.json					# 依赖包
│  package.json							# 依赖包
│  README.md							# 说明文档
│  yarn.lock
│
├─build 								# Webpack配置
│      build.js
│      check-versions.js
│      utils.js
│      vue-loader.conf.js
│      webpack.base.conf.js
│      webpack.dev.conf.js
│      webpack.prod.conf.js
│
├─config								# 核心配置文件夹
│      dev.env.js
│      index.js
│      prod.env.js
│
├─dist									# 打包文件
├─src									# 开发目录
│  │  App.vue							# 入口文件
│  │  main.js							# 项目核心文件
│  │
│  ├─api								# 通用方法
│  │      common.js						# 数据处理正则验证
│  │      initApi.js					# 数据处理
│  │
│  ├─assets 							# 资源目录
│  │  ├─img
│  │  │  │  read_off.png
│  │  │  │  read_on.png
│  │  │  │  write_off.png
│  │  │  │  write_on.png
│  │  │  │
│  │  │  └─dark
│  │  │          read_off.png
│  │  │          read_on.png
│  │  │          write_off.png
│  │  │          write_on.png
│  │  │
│  │  └─lottieWeb
│  │      │  switch.json
│  │      │
│  │      └─dark 						# 暗黑模式资源
│  │              switch.json
│  │
│  ├─components 						# 组件
│  │      Cell.vue						# 设置页面右箭头组件
│  │      HomeBanner.vue				# 主页banner组件
│  │      HomeCellBox.vue				# 主页功能容器组件
│  │      HomeHeader.vue				# 标题组件
│  │      HomeMode.vue					# 控件组件
│  │      LocationChoose.vue			# 摆放位置弹框组件
│  │      message.vue					# 检查更新和删除弹窗组件
│  │      messageText.vue				# 阻止共享弹窗组件
│  │      ModifyName.vue				# 设备名称修改组件
│  │
│  ├─hilink								# 页面
│  │      prodInfo.js					# 产品信息数据
│  │      profile.js					# 动态变化的产品数据
│  │
│  ├─i18n								# H5卡片语言显示
│  │  │  index.js						# 控制语言切换
│  │  │
│  │  └─language						# 保存语言文件目录
│  │          en-US.json				# 英文文件
│  │          zh-CN.json				# 中文文件
│  │
│  ├─router 							# 路由配置
│  │      index.js
│  │
│  ├─store								# 全局数据及方法保存目录
│  │      actions.js					# HiLink接口
│  │      index.js
│  │      mutations.js 					# 数据处理
│  │      state.js						# 保存全局变量
│  │
│  └─views								# 页面
│      │  Home.vue						# 主页面
│      │  Setting.vue					# 设置页面
│      │
│      ├─img
│      │  │  read_off.png
│      │  │  read_on.png
│      │  │  write_off.png
│      │  │  write_on.png
│      │  │
│      │  └─dark
│      │          read_off.png
│      │          read_on.png
│      │          write_off.png
│      │          write_on.png
│      │
│      └─lottieWeb
│          │  switch.json
│          │
│          └─dark
│                  switch.json
│
└─static								# 静态资源
    │  .gitkeep
    │
    ├─css								# 样式文件
    │      common.css					# 基础样式
    │
    └─img								# 图片资源
        │  iconD.png
        │  ic_back.png
        │  ic_more.png
        │  ic_mores.png
        │  ic_switch_off.png
        │  ic_switch_on.png
        │  logo.png
        │  radiobutton_light.png
        │  radiobutton_off.png
        │
        └─dark							# 暗黑模式图片资源
                ic_back.png
                ic_more.png
                ic_mores.png
                ic_switch_off_dark.png
                ic_switch_on_dark.png
                radiobutton_light.png
                radiobutton_off.png
```

## 调试方式

伙伴开发阶段时候，可以在index.html中加上如下代码进行代码调试。

```html
<script src="//cdn.jsdelivr.net/npm/eruda"></script>
<script>
	window.hilink && eruda.init();
</script>
```

# 参考

- [vuejs-templates](http://vuejs-templates.github.io/webpack/)
- [docs for vue-loader](http://vuejs.github.io/vue-loader)
- [华为智能硬件合作伙伴 > 应用开发 > 智慧生活App](https://device.harmonyos.com/cn/docs/devicepartner/DevicePartner-Guides/ai-life-app-cloud-services-0000001087684232)
- [华为智能硬件合作伙伴 > Wi-Fi设备JSAPI接口参考](https://device.harmonyos.com/cn/docs/devicepartner/DevicePartner-References/wifi-overview-0000001077756430)

- [华为智能硬件合作伙伴 > 蓝牙设备JSAPI接口参考](https://device.harmonyos.com/cn/docs/devicepartner/DevicePartner-References/bluetooth-overview-0000001090562401)
