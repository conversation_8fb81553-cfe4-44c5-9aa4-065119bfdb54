@import url("../../lib/style/public.less");

#service-account-page {
    overflow-y: auto;
    background-color: var(--emui_color_subbg);

    .content {
        margin: 6.6rem 0.6rem 0px 0.6rem;
        position: relative;
        padding-bottom: 2.4rem;
    }

    // 二维码区域
    .qrcode-section {
        margin-bottom: 1.2rem;
        text-align: center;
        padding: 2.4rem 1.2rem;

        .qrcode-card {
            display: flex;
            flex-direction: column;
            align-items: center;

            .qrcode-image {
                width: 16rem;
                height: 16rem;
                object-fit: contain;
                border-radius: 0.8rem;
            }
        }
    }

    // 关注方式区域
    .method-section {
        .cardStyle();
        margin-bottom: 1.2rem;

        .method-item {
            position: relative;
            padding: 1.6rem 0;
            height: 8rem;

            .method-content {
                display: flex;
                flex-direction: column;
                gap: 0.8rem;

                .method-title {
                    font-size: 1.4rem;
                    color: var(--emui_text_primary);
                    font-weight: 500;
                    line-height: 1.4;
                }

                .method-desc {
                    font-size: 1.4rem;
                    color: var(--emui_text_secondary);
                    line-height: 1.5;
                    word-wrap: break-word;
                }
            }

            // 决策理由：复制按钮使用绝对定位放在右下角
            .copy-btn {
                position: absolute;
                bottom: 1.6rem;
                right: -0.6rem;
                border: none;
                border-radius: 0.4rem;
                color: var(--emui_accent);
                font-size: 1.2rem;
                cursor: pointer;
                transition: all 0.2s ease;
                white-space: nowrap;
            }
        }
    }
}

// 决策理由：适配平板设备的边距
.pad #service-account-page {
    .content {
        margin: 6.6rem var(--home_margin) 0px var(--home_margin);
    }
}
