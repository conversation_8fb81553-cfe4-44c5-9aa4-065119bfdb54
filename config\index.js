// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require("path");
const os = require("os");

// decide which path use to build for the project
// 如果contents === debug  则 在main.js中会动态引入 eruda
let myPath = `../dist/h5_001/`;

// 获取本机IP地址
function getIPAdress() {
    let interfaces = os.networkInterfaces(),
        iface,
        alias;
    for (let devName in interfaces) {
        iface = interfaces[devName];
        for (let i = 0, len = iface.length; i < len; i++) {
            alias = iface[i];
            if (
                alias.family === "IPv4" &&
                alias.address !== "127.0.0.1" &&
                /192.168/.test(alias.address) &&
                !alias.internal
            ) {
                return alias.address;
            }
        }
    }
}

module.exports = {
    dev: {
        // Paths
        assetsSubDirectory: "static",
        assetsPublicPath: "/",
        proxyTable: {},
        // Various Dev Server settings
        host: getIPAdress()? getIPAdress(): "localhost",
        port: process.env.PORT || 8080,
        autoOpenBrowser: false,
        errorOverlay: true,
        notifyOnErrors: true,
        poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-
        // Use Eslint Loader?
        // If true, your code will be linted during bundling and
        // linting errors and warnings will be shown in the console.
        useEslint: true,
        // If true, eslint errors and warnings will also be shown in the error overlay
        // in the browser.
        showEslintErrorsInOverlay: false,
        /**
         * Source Maps
         */
        // https://webpack.js.org/configuration/devtool/#development
        devtool: "cheap-module-eval-source-map",
        // If you have problems debugging vue-files in devtools,
        // set this to false - it *may* help
        // https://vue-loader.vuejs.org/en/options.html#cachebusting
        cacheBusting: true,
        cssSourceMap: false
    },
    //打包处理
    build: {
        // Template for index.html
        index: path.resolve(__dirname, myPath + "index.html"),
        // Paths
        assetsRoot: path.resolve(__dirname, myPath),
        assetsSubDirectory: "static",
        assetsPublicPath: "./",
        /**
         * Source Maps
         */

        productionSourceMap: false,
        // https://webpack.js.org/configuration/devtool/#production
        devtool: "#source-map",

        // Gzip off by default as many popular static hosts such as
        // Surge or Netlify already gzip all static assets for you.
        // Before setting to `true`, make sure to:
        // npm install --save-dev compression-webpack-plugin
        productionGzip: false,
        productionGzipExtensions: ["js", "css"],

        // Run the build command with an extra argument to
        // View the bundle analyzer report after build finishes:
        // `npm run build --report`
        // Set to `true` or `false` to always turn it on or off
        bundleAnalyzerReport: process.env.npm_config_report
    }
};
