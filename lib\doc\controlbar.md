# ControlBar

| prop参数 | 描述 |
| --- | --- |
| name                  | 控制器名称 String|
| iconName         | 控制器图标 String 只需要图片前缀，图片放置在assets/目录下 图片名称结尾是 _on.png(开启), _off.png（关闭）|
| disabled           | 是否禁用 Bool |
| info               | 控制器信息|
| active                | 激活状态, Bool  |
| spinnerItems         | 下拉框控制数据集合value和name, Bool  |
| spinnerValue         | 选择的下拉框值, Number  |

| event | 描述 |
| --- | --- |
|  handleClick        | 事件 点击事件透传|

### 1.常用开启和关闭功能

#### ![](./images/normal_switch.png)

```` JavaScript
<ControlBar
  :name="$t('Heating')"
  iconName="ic_foot"
  @handleClick="handleClick"
  :disabled="!(canControl && Switch == 1)"
  :active="Heating == 1 && canControl && Switch == 1">
</ControlBar>
````

### 2.常用的设置档位模式 点击弹出选择组件的功能 自定义click事件

#### ![](./images/zdy_switch.jpg)

```` JavaScript
<ControlBar :name="$t('amdw')"
  iconName="ic_plus"
  @handleClick="onClicked"
  :disabled="!(canControl && Switch == 1)"
  :spinnerItems="messageStallItems"
  :info="messageStallText"
  :spinnerValue="messageStall"
  active="messageStall > 0">
</ControlBar>
````
