# SliderBar

#### 滑动选择器

| prop参数 | 描述 |
| --- | --- |
| name    | 名称, String|
| info    | 数据信息, String|
| value   |选中值, Number|
| showXAxis   |是否显示刻度, Bool|
| step   |步长, Number 默认1|
| min   |最小值, Number 默认0|
| max   |最大值, Number 默认100|
| disabled   |是否禁用, Bool|


| Event | 描述 |
| --- | --- |
| change   |滑动结束|

#### 与ControlBar一起使用示例

#### ![](./images/sliderbar.jpg)

```` JavaScript
<SliderBar :name="$t('timeoff')" :info="timeInfo" v-model="timeSliderValue" :showXAxis="true" :step="10" :min="0"
  :max="90" @change="onTimeChanged" :disabled="!(canControl && Switch == 1)"></SliderBar>
````
