export const goBack = {
    computed: {
        dialogShowList() {
            // 弹框集合标志位
            let resultArr = [];
            if (this.dialogList) {
                for (let val of this.dialogList) {
                    if (this[val]) {
                        resultArr.push(val);
                    }
                }
            }
            return resultArr;
        }
    },
    activated() {
        window.goBack = () => {
            if (this.dialogShowList.length === 0) {
                this.$router.goBack();
            } else {
                for (let val of this.dialogShowList) {
                    this[val] = false;
                }
            }
        };
    }
};


