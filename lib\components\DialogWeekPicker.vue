<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@talkweb.com.cn
 * @Date: 2025-06-17 16:00:00
 * @LastEditTime: 2025-06-17 16:00:00
 * @LastEditors: <EMAIL>
-->
<template>
    <div class="dialog">
        <div class="dialog-container">
            <div class="mod-head">
                <span id="com.huawei.smarthome:id/dialog_week_title" class="title">{{ title }}</span>
                <span class="subtitle" v-if="subtitle">{{ subtitle }}</span>
            </div>
            <!-- 星期选择列表 -->
            <div class="week-list">
                <div class="week-item" v-for="(day, index) in weekDays" :key="index" @click="toggleDay(index)">
                    <label class="week-label">
                        <span class="label-title">{{ day.name }}</span>
                        <span class="week-checkbox">
                            <input :id="'week_' + index" class="week-checkbox-input" type="checkbox"
                                v-model="selectedDays" :value="index">
                            <span class="week-checkbox-core"></span>
                        </span>
                    </label>
                    <div class="divider" v-if="index !== weekDays.length - 1"></div>
                </div>
            </div>

            <div class="dialog-btns">
                <div id="com.huawei.smarthome:id/dialog_week_cancel" @click.stop="cancel(true)">
                    <p>{{ $t("cancel") }}</p>
                </div>
                <span class="line"></span>
                <div id="com.huawei.smarthome:id/dialog_week_confirm" @click.stop="confirm">
                    <p>{{ $t("ok") }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        title: {
            default: "",
            type: String,
        },
        subtitle: {
            default: null,
            type: String,
        },
        defaultSelected: {
            default: () => [],
            type: Array,
        },
    },
    name: "DialogWeekPicker",
    data () {
        return {
            selectedDays: [...this.defaultSelected],
            weekDays: [
                { name: this.$t("monday"), value: 0 },
                { name: this.$t("tuesday"), value: 1 },
                { name: this.$t("wednesday"), value: 2 },
                { name: this.$t("thursday"), value: 3 },
                { name: this.$t("friday"), value: 4 },
                { name: this.$t("saturday"), value: 5 },
                { name: this.$t("sunday"), value: 6 },
            ]
        };
    },
    computed: {
        isAllSelected () {
            return this.selectedDays.length === this.weekDays.length;
        }
    },
    methods: {
        cancel (check) {
            this.$emit("cancel", check);
        },
        confirm () {
            this.$emit("confirm", this.selectedDays);
        },
        toggleDay (index) {
            const dayIndex = this.selectedDays.indexOf(index);
            if (dayIndex > -1) {
                this.selectedDays.splice(dayIndex, 1);
            } else {
                this.selectedDays.push(index);
            }
        },
        toggleSelectAll () {
            if (this.isAllSelected) {
                this.selectedDays = [];
            } else {
                this.selectedDays = this.weekDays.map((_, index) => index);
            }
        }
    },
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.week-list {
    padding: 0 2.4rem;
    overflow-y: auto;
}

.week-item {
    cursor: pointer;
    position: relative;

    .week-label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        // padding: 0.6rem 0;
        cursor: pointer;

        .label-title {
            font-size: 1.6rem;
            font-weight: 500;
            color: var(--emui_text_primary);
        }
    }

    .divider {
        position: absolute;
        height: 1px;
        left: 0;
        right: 0;
        bottom: 0;
        transform: scaleY(0.25);
        background: var(--emui_color_divider_horizontal);
    }
}

.week-checkbox {
    position: relative;

    .week-checkbox-input {
        display: none;
    }

    .week-checkbox-core {
        box-sizing: border-box;
        display: inline-block;
        background-color: var(--emui_radio_bg);
        border-radius: 0.4rem;
        border: 1px solid var(--emui_radio_border);
        position: relative;
        width: 2rem;
        height: 2rem;
        vertical-align: middle;
        transition: all 0.2s ease;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 0.6rem;
            height: 1rem;
            border: 2px solid #fff;
            border-top: none;
            border-left: none;
            transform-origin: center;
            transition: all 0.2s ease;
        }
    }

    .week-checkbox-input:checked+.week-checkbox-core {
        background-color: var(--emui_slide_progress_bg);
        border-color: var(--emui_slide_progress_bg);

        &::after {
            transform: translate(-50%, -60%) rotate(45deg) scale(1);
        }
    }
}
</style>
