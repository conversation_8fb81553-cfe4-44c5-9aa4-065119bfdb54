# DialogPicker

#### 弹窗单选项组件

| prop参数 | 描述 |
| --- | --- |
| unit    | 选项单位, String|
| values   |选项值列表, Array|
| valueKey   |选项文字的key,String|
| title   |弹窗标题, String|
| defaultIndex   |默认选择项索引, Number|
| leftBtnText   |左边按钮文本, 一般在设置定时时候左右按钮可以关闭场景|
| leftBtnRed   |左边按钮红色警告，例如关闭时候|


| Event | 描述 |
| --- | --- |
| cancel   |取消事件|
| confirm   |确定事件|

#### ![](./images/dialog_picker.png)

```` JavaScript
timingValues: [
  {name: '5', value: 0},
  {name: '10', value: 1},
  {name: '15', value: 2},
  {name: '20', value: 3},
  {name: '25', value: 4},
  {name: '30', value: 5}
]
<DialogPicker v-if="isShowTimingPicker" :unit="$t('Timing_unit')" :values="timingValues" valueKey="name"
                  :title="$t('Timing')" :defaultIndex="Timing"
                  @cancel="isShowTimingPicker = false" @confirm="onTimingConfirm"></DialogPicker>
````

