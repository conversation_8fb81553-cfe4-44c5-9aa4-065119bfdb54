<template>
    <div class="yearly-calendar">
        <!-- 月份网格 -->
        <div class="months-grid">
            <div
                v-for="month in months"
                :key="month.value"
                :class="getMonthClasses(month)"
                @click="onMonthClick(month)"
            >
                <span class="month-text">{{ month.label }}</span>
                <!-- 数据可用性指示器 -->
                <div v-if="hasDataForMonth(month.value)" class="data-indicator"></div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'YearlyCalendar',
    props: {
        // 当前显示的年份
        currentDate: {
            type: Date,
            default: () => new Date()
        },
        // 选中的年月
        selectedDate: {
            type: Date,
            default: null
        },
        // 最小日期
        minDate: {
            type: Date,
            default: null
        },
        // 最大日期
        maxDate: {
            type: Date,
            default: null
        },
        // 数据可用性信息 - 包含有数据的月份数组
        dataAvailability: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            months: [
                { value: 1, label: '1' },
                { value: 2, label: '2' },
                { value: 3, label: '3' },
                { value: 4, label: '4' },
                { value: 5, label: '5' },
                { value: 6, label: '6' },
                { value: 7, label: '7' },
                { value: 8, label: '8' },
                { value: 9, label: '9' },
                { value: 10, label: '10' },
                { value: 11, label: '11' },
                { value: 12, label: '12' }
            ]
        };
    },
    computed: {
        currentYear() {
            return this.currentDate ? this.currentDate.getFullYear() : new Date().getFullYear();
        }
    },
    methods: {
        // 获取月份的CSS类
        getMonthClasses(month) {
            const classes = ['month-item'];
            
            // 选中状态
            if (this.isMonthSelected(month)) {
                classes.push('selected');
            }
            
            // 当前月份
            if (this.isCurrentMonth(month)) {
                classes.push('current');
            }
            
            // 禁用状态
            if (this.isMonthDisabled(month)) {
                classes.push('disabled');
            }
            
            return classes;
        },
        
        // 判断月份是否被选中
        isMonthSelected(month) {
            if (!this.selectedDate) return false;
            
            return this.selectedDate.getFullYear() === this.currentYear &&
                   this.selectedDate.getMonth() + 1 === month.value;
        },
        
        // 判断是否为当前月份
        isCurrentMonth(month) {
            const today = new Date();
            return today.getFullYear() === this.currentYear &&
                   today.getMonth() + 1 === month.value;
        },
        
        // 判断月份是否禁用
        isMonthDisabled(month) {
            const monthDate = new Date(this.currentYear, month.value - 1, 1);
            
            if (this.minDate && monthDate < new Date(this.minDate.getFullYear(), this.minDate.getMonth(), 1)) {
                return true;
            }
            
            if (this.maxDate && monthDate > new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), 1)) {
                return true;
            }
            
            return false;
        },
        
        // 判断月份是否有数据
        hasDataForMonth(month) {
            return this.dataAvailability.includes(month);
        },
        

        
        // 月份点击事件
        onMonthClick(month) {
            if (this.isMonthDisabled(month)) return;
            
            // 创建选中的日期（该月的第一天）
            const selectedDate = new Date(this.currentYear, month.value - 1, 1);
            this.$emit('month-select', selectedDate);
        }
    }
};
</script>

<style scoped>
.yearly-calendar {
    padding: 0;
    background: transparent;
}

.months-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0;
    padding: 0 2.4rem;
}

.month-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.6rem !important;
    cursor: pointer;
    transition: all 0.2s ease;
}

.month-item.selected .month-text {
    background-color: var(--emui_accent);
    color: white;
    border-radius: 50%;
    width: 2.8rem;
    height: 2.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}



.month-item.disabled {
    cursor: not-allowed;
}

.month-item.disabled .month-text {
    color: var(--emui_text_secondary);
    opacity: 0.5;
}

.month-item:hover:not(.disabled):not(.selected) .month-text {
    background-color: rgba(var(--emui_accent_rgb), 0.1);
    border-radius: 50%;
    width: 2.8rem;
    height: 2.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.month-text {
    font-size: 1.4rem;
    font-weight: 500;
    color: var(--emui_text_primary);
    transition: all 0.2s ease;
    position: relative;
    z-index: 2;
}

.data-indicator {
    position: absolute;
    bottom: 0.4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 0.4rem;
    height: 0.4rem;
    background-color: var(--emui_accent);
    border-radius: 50%;
    z-index: 1;
}

.month-item.selected .data-indicator {
    display: none;
}
</style>
