<template>
  <div class="dialog" @click="cancel(false)">
    <div class="dialog-container">
      <div class="mod-head">
        <span id="com.huawei.smarthome:id/dialog_picker_title" class="title">{{ title }}</span>
        <span class="subtitle" v-if="subtitle">{{subtitle}}</span>
      </div>
      <picker class="picker" :slots="slots" :valueKey="valueKey" :centerHeight='centerHeight' :itemHeight="itemHeight"
              :unit="unit"
              @change="onValuesChange" :rotateEffect="true"></picker>
      <div class="dialog-btns">
        <div id="com.huawei.smarthome:id/dialog_picker_cancel" @click.stop='cancel(true)'>
          <p :class="{'red' :leftBtnRed}">{{ leftBtnText || $t('cancel') }}</p>
        </div>
        <span class="line"></span>
        <div id="com.huawei.smarthome:id/dialog_picker_confirm" @click.stop='confirm'>
          <p>{{ $t('ok') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import picker from './picker/picker';

export default {
  props: {
    title: {
      default: '',
      type: String
    },
    subtitle: {
      default: null,
      type: String
    },
    unit: {
      default: null,
      type: String
    },
    values: {
      default: Array(),
      type: Array
    },
    valueKey: {
      default: null,
      type: String
    },
    defaultIndex: {
      default: 0,
      type: Number
    },
    leftBtnText: {
      default: null,
      type: String
    },
    leftBtnRed: {
      default: false,
      type: Boolean
    }
  },
  name: 'DialogPicker',
  components: {picker},
  data() {
    return {
      curValue: '1'
    };
  },
  computed: {
    centerHeight() {
      let fontSize = parseFloat(document.querySelector('html')
        .style
        .fontSize
        .replace('px', ''));
      return 56 * (fontSize / 10);
    },
    itemHeight() {
      let fontSize = parseFloat(document.querySelector('html')
        .style
        .fontSize
        .replace('px', ''));
      return 36 * (fontSize / 10);
    },
    slots() {
      return [
        {
          flex: 1,
          values: this.values,
          className: 'slot1',
          textAlign: 'center',
          defaultIndex: this.defaultIndex
        }
      ];
    }
  },
  methods: {
    cancel(check) {
      this.$emit('cancel', check);
    },
    confirm() {
      this.$emit('confirm', this.curValue);
    },
    onValuesChange(picker, values) {
      this.curValue = values[0];
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.red{
  color: var(--emui_functional_red);
}

.picker {
  margin: 0.8rem 2.4rem 0px 2.4rem;
}
</style>
