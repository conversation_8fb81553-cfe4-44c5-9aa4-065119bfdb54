<template>
    <div class="section-info" :class="{'disabled' : (disabled && !inside), 'inside' : inside}">
        <div class="mode-box">
            <slot></slot>
            <div class="divider" v-if="divider"></div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'InfoBar',
    props: {
        disabled: {
            default: true,
            type: Boolean
        },
        inside: {
            type: <PERSON><PERSON>an,
            default: false
        },
        divider: {
            type: <PERSON>olean,
            default: false
        }
    }
};
</script>

<style lang='less' scoped>
@import url("../style/public.less");

.section-info {
    width: 100%;
    .cardStyle();
    position: relative;
    padding: 0px 0.8rem;

    &.disabled {
        opacity: .4;
    }

    &.inside {
        margin: 0px;
        padding: 0px;
    }

    .mode-box {
        position: relative;
        color: var(--emui_text_primary);
        height: 8.8rem;
        display: flex;
        padding: 0px;
        align-items: center;

        .divider {
            position: absolute;
            width: calc(100% - 1.6rem);
            height: 1px;
            bottom: 0;
            left: 0.8rem;
            transform: scaleY(0.25);
            background: var(--emui_color_divider_horizontal);
        }
    }
}

.pad #home .section-info {
    padding: 0px;
}

.tahiti #home .section-info {
    padding: 0px;
}

</style>
