<template>
    <div id="sleep-page">
        <Titlebar :title="$t('sleep_title')" :showRightIcon="false" @leftClick="$router.goBack()" />
        <div class="content" :style="{ paddingTop: `${headerHeight}px` }">
            <div class="module-box">
                <!-- 睡眠设置区域 -->
                <List-Container style="width: auto">
                    <List-Item-Switch :name="$t('sleep_mode')" :active="sleepModeEnabled" @handleClick="toggleSleepMode"
                        idStr="sleep_mode" :divider="sleepModeEnabled" />
                    <!-- 睡眠模式开启时显示的设置项 -->
                    <template v-if="sleepModeEnabled">
                        <List-Item-Time :name="$t('start_time')" :info="startInfo" :defaultIndex="startIndex"
                            :subtitle="subtitle" :countDown="false" :limitLength="limitLength" :startTime="startTime"
                            @select="(val) => select(1, val)" idStr="start_time" key="start"
                            v-model="isShowStartTimeDialog" />
                        <List-Item-Time :name="$t('end_time')" :info="endInfo" :defaultIndex="endIndex"
                            :subtitle="subtitle" :countDown="false" :startTime="startTime" :limitLength="limitLength"
                            :divider="false" @select="(val) => select(2, val)" idStr="end_time" key="end"
                            v-model="isShowEndTimeDialog" />
                    </template>
                </List-Container>

                <!-- 睡眠模式开启时显示的其他设置项 -->
                <template v-if="sleepModeEnabled">
                    <div class="class-mode-item">
                        <List-Item-Radio style="width: 100%; padding: 0" :name="$t('repeat')" :info="repeatInfo"
                            :options="repeatOptions" :defaultIndex="repeatIndex" :showOk="false" @select="selectRepeat"
                            idStr="repeat" :divider="false" v-model="isShowRepeatDialog" />
                    </div>
                    <div class="class-mode-item" style="margin-top: 1.2rem">
                        <List-Item-Switch style="width: 100%; padding: 0" :name="$t('alarm_light_display')"
                            :desc="$t('alarm_light_desc')" :active="alarmLightEnabled" @handleClick="toggleAlarmLight"
                            idStr="alarm_light" :divider="false" />
                    </div>
                </template>



                <!-- 保存按钮 -->
                <div class="save-button-container">
                    <div class="save-button" @click="saveSleepSettings">
                        {{ $t("save") }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 自定义重复对话框 -->
        <DialogWeekPicker v-if="isShowCustomWeekDialog" :title="$t('custom_week_title')"
            :defaultSelected="customWeekDays" @cancel="cancelCustomWeek" @confirm="confirmCustomWeek" />
    </div>
</template>

<script>
import { mapState, mapGetters } from "vuex";
import { goBack } from "../util/mixins";

export default {
    name: "SleepPage",
    mixins: [goBack],
    data () {
        return {
            // 弹窗管理 - 用于goBack mixin
            dialogList: [
                'isShowCustomWeekDialog',
                'isShowStartTimeDialog',
                'isShowEndTimeDialog',
                'isShowRepeatDialog'
            ],
            subtitle: "", // 弹窗子标题
            startIndex: null, // 开始时间默认值（未设置）
            endIndex: null, // 结束时间默认值（未设置）
            startTime: 0, // 开始时间
            limitLength: 1439, // 时长
            list: [], // 时间数组
            repeatIndex: 0, // 重复设置选中索引
            repeatOptions: [],
            sleepModeEnabled: false, // 睡眠模式开关状态
            alarmLightEnabled: true, // 告警灯开关状态 - 默认开启
            isShowCustomWeekDialog: false, // 自定义重复对话框显示状态
            customWeekDays: [], // 自定义选择的星期几 [0-6] 周一到周日
            hasExistingTimer: false, // 是否存在现有定时器
            // 子组件弹窗状态追踪
            isShowStartTimeDialog: false,
            isShowEndTimeDialog: false,
            isShowRepeatDialog: false,
        };
    },
    activated () { },
    created () {
        this.initializeRepeatOptions();
    },
    mounted () {
        this.initializeData();
    },
    watch: {
        // 监听store中的告警灯状态变化
        IndicatorOn: {
            handler (val) {
                if (val !== undefined) {
                    this.alarmLightEnabled = val == 1;

                }
            },
            immediate: true
        },

        Timer: {
            handler (val) {


                // 使用实际返回的数据结构：val.timer
                if (val && val.timer && Array.isArray(val.timer) && val.timer.length > 0) {
                    const timerItem = val.timer[0];

                    this.sleepModeEnabled = timerItem.enable == 1;
                    // 标记存在现有定时器
                    this.hasExistingTimer = true;


                    // 处理开始时间 - 支持UTC格式和数值格式
                    const startTimeIndex = this.parseTimeValue(timerItem.start);
                    this.startIndex = startTimeIndex !== null ? startTimeIndex : null;


                    // 处理结束时间 - 支持UTC格式和数值格式
                    const endTimeIndex = this.parseTimeValue(timerItem.end);
                    this.endIndex = endTimeIndex !== null ? endTimeIndex : null;


                    // 解析重复设置 - 支持数值和字符串格式
                    const week = timerItem.week || 0;

                    this.parseWeekToRepeatIndex(week);

                } else {

                    // 标记不存在现有定时器，使用初始化状态
                    this.hasExistingTimer = false;

                    // 使用初始化的默认状态
                    this.sleepModeEnabled = false;  // 初始化：关闭睡眠模式
                    this.alarmLightEnabled = true;  // 初始化：开启告警灯
                    this.startIndex = null;         // 初始化：开始时间未设置
                    this.endIndex = null;           // 初始化：结束时间未设置
                    this.repeatIndex = 0;           // 初始化：不重复
                    this.customWeekDays = [];       // 初始化：清空自定义重复天数

                    console.log('� 使用初始化状态:', {
                        hasVal: !!val,
                        hasTimer: !!(val && val.timer),
                        timerLength: val && val.timer ? val.timer.length : 0,
                        isArray: Array.isArray(val && val.timer),
                        fullData: val,
                        存在现有定时器: this.hasExistingTimer,
                        睡眠模式: this.sleepModeEnabled,
                        告警灯: this.alarmLightEnabled,
                        开始时间索引: this.startIndex,
                        结束时间索引: this.endIndex,
                        重复索引: this.repeatIndex,
                        自定义重复天数: this.customWeekDays
                    });
                }
            },
            immediate: true,
            deep: true
        },

        // 监听自定义重复弹窗关闭，弹出上一个弹窗
        isShowCustomWeekDialog (newVal, oldVal) {
            console.log('🔄 自定义重复弹窗状态变化:', { oldVal, newVal });
            // 当自定义重复弹窗从显示变为隐藏时
            if (oldVal === true && newVal === false) {
                console.log("🔄 自定义重复弹窗关闭，准备打开重复设置弹窗");
                this.$nextTick(() => {
                    this.isShowRepeatDialog = true;
                    this.repeatIndex = 0; // 重置为不重复
                    console.log("✅ 重复设置弹窗已重新打开");
                });
            }
        }
    },
    computed: {
        ...mapState(["loginUser", "deviceInfo"]),
        ...mapGetters(["headerHeight", "statusBarHeight", "canControl"]),
        ...mapState({
            Timer: (state) => state.timer,
            IndicatorOn: (state) => state.indicator.on,
        }),
        start () {
            return this.Timer && this.Timer.timer && this.Timer.timer.length > 0
                ? this.Timer.timer[0].start : 0;
        },
        end () {
            return this.Timer && this.Timer.timer && this.Timer.timer.length > 0
                ? this.Timer.timer[0].end : 0;
        },

        startInfo () {
            return this.formatTimeDisplay(this.startIndex);
        },
        endInfo () {
            // 判断是否为跨日时间：开始时间晚于结束时间
            const isNextDay = !this.isTimeNotSet(this.startIndex) &&
                !this.isTimeNotSet(this.endIndex) &&
                this.startIndex > this.endIndex;

            return this.formatTimeDisplay(this.endIndex, isNextDay);
        },
        repeatInfo () {
            // 确保 repeatOptions 已初始化且 repeatIndex 是有效数字
            if (!this.repeatOptions || this.repeatOptions.length === 0) {
                return this.$t("no_repeat");
            }

            const selectedOption = this.repeatOptions.find(
                (option) => option.value === this.repeatIndex
            );

            if (!selectedOption) {
                return this.$t("no_repeat");
            }
            if (selectedOption.value == 0) {
                return this.$t("no_repeat");
            } else if (selectedOption.value == 4) {
                // 自定义重复，显示选择的星期几
                if (this.customWeekDays.length === 0) {
                    return this.$t("custom_repeat");
                }
                const weekNames = [
                    this.$t("monday"), this.$t("tuesday"), this.$t("wednesday"),
                    this.$t("thursday"), this.$t("friday"), this.$t("saturday"), this.$t("sunday")
                ];
                const selectedWeekNames = this.customWeekDays.map(index => weekNames[index]);
                return selectedWeekNames.join('、');
            } else {
                return selectedOption.name;
            }
        },
    },
    methods: {
        // 判断时间是否真正未设置
        isTimeNotSet (timeIndex) {
            return timeIndex == null || timeIndex == undefined || timeIndex === "";
        },

        // 格式化时间显示，支持次日标识
        formatTimeDisplay (timeIndex, isNextDay = false) {
            if (this.isTimeNotSet(timeIndex)) {
                return this.$t("no_setting");
            }

            let hour = parseInt(timeIndex / 60) >= 10
                ? parseInt(timeIndex / 60)
                : "0" + parseInt(timeIndex / 60);
            let min = timeIndex % 60 >= 10
                ? timeIndex % 60
                : "0" + (timeIndex % 60);

            const timeStr = hour + ":" + min;
            return isNextDay ? this.$t("next_day") + " " + timeStr : timeStr;
        },

        // 选择时间
        select (type, value) {
            if (type == 1) {
                this.startIndex = value;
            } else {
                this.endIndex = value;
            }
        },
        // 切换睡眠模式
        toggleSleepMode () {
            this.sleepModeEnabled = !this.sleepModeEnabled;
        },
        // 选择重复设置
        selectRepeat (value) {
            console.log("selectRepeat received value:", value, typeof value);
            // 确保 value 是数字类型
            this.repeatIndex = typeof value === 'string' ? parseInt(value, 10) : value;

            // 如果选择自定义，先关闭重复设置弹窗，再弹出自定义重复对话框
            if (this.repeatIndex === 4) {
                this.isShowRepeatDialog = false; // 先关闭重复设置弹窗
                this.$nextTick(() => {
                    this.isShowCustomWeekDialog = true; // 再弹出自定义重复弹窗
                });
            } else {
                // 非自定义选择，关闭重复设置弹窗
                this.isShowRepeatDialog = false;
            }

            console.log("repeatIndex updated to:", this.repeatIndex);
        },
        // 切换告警灯
        toggleAlarmLight () {
            this.alarmLightEnabled = !this.alarmLightEnabled;
        },

        // 取消自定义重复选择
        cancelCustomWeek () {
            this.isShowCustomWeekDialog = false;
            // watcher会自动处理重新打开重复设置弹窗和重置repeatIndex
        },

        // 确认自定义重复选择
        confirmCustomWeek (selectedDays) {
            this.isShowCustomWeekDialog = false;
            this.customWeekDays = selectedDays;
            console.log("Custom week days selected:", selectedDays);
            // 确认后不需要重新打开重复设置弹窗，直接完成选择
        },
        // 保存睡眠设置
        async saveSleepSettings () {
            try {
                // 验证：如果睡眠模式开启，必须设置开始时间和结束时间
                if (this.sleepModeEnabled) {
                    if (this.isTimeNotSet(this.startIndex)) {
                        this.$toast("请设置开始时间");
                        return;
                    }
                    if (this.isTimeNotSet(this.endIndex)) {
                        this.$toast("请设置结束时间");
                        return;
                    }

                    // 1. 先单独下发告警灯设置
                    await this.saveAlarmLightSetting();
                    // 2. 再下发定时器设置（只包含sleepMode）
                    setTimeout(async () => {
                        await this.saveSleepModeTimer();
                    }, 300)
                    this.$toast(this.$t("save_success"));
                } else {
                    // 睡眠模式关闭时：先单独下发睡眠模式关闭
                    await this.saveSleepModeOff();
                    if (this.Timer.timer.length > 0) {
                        const timerData = {
                            action: 2,  // 根据逻辑确定的action值
                            timer: [{
                                id: 1
                            }]
                        };
                        // 下发命令到设备 - 使用正确的数据格式
                        const commandData = {
                            timer: timerData  // 直接使用timer作为服务ID
                        };

                        console.log('🌙 最终定时器下发数据:', JSON.stringify(commandData, null, 2));
                        this.$store.dispatch("setDevInfo", commandData)
                            .then(() => {
                                // 删除定时器后，标记为无现有定时器
                                this.hasExistingTimer = false;
                                this.$toast(this.$t("save_success"));
                            })
                            .catch((error) => {
                                console.error("❌ 睡眠模式定时器设置失败:", error);
                                throw error; // 重新抛出错误，让上层处理
                            });
                    } else {
                        this.$toast(this.$t("save_success"));
                    }

                }



            } catch (error) {
                console.error("❌ 睡眠设置保存失败:", error);
                this.$toast(this.$t("save_failed"));
            }
        },

        // 保存告警灯设置（单独下发）
        async saveAlarmLightSetting () {
            if (!this.canControl) {
                throw new Error(this.$t("device_not_controllable"));
            }



            const alarmLightData = {
                indicator: {
                    on: this.alarmLightEnabled ? 1 : 0
                }
            };

            console.log('💡 告警灯下发数据:', JSON.stringify(alarmLightData, null, 2));

            return this.$store.dispatch("setDevInfo", alarmLightData)
                .then(() => {
                    // 告警灯设置保存成功
                })
                .catch((error) => {
                    console.error("❌ 告警灯设置失败:", error);
                });
        },

        // 单独下发睡眠模式关闭
        async saveSleepModeOff () {
            const sleepModeOffData = {
                sleepMode: {
                    on: 0  // 关闭睡眠模式
                }
            };

            return this.$store.dispatch("setDevInfo", sleepModeOffData)
                .then(() => {
                    // 睡眠模式关闭成功
                })
                .catch((error) => {
                    console.error("❌ 睡眠模式关闭失败:", error);
                    throw error;
                });
        },

        // 保存睡眠模式定时器（只包含sleepMode）
        async saveSleepModeTimer () {
            // 确定action值的逻辑
            let actionValue;
            if (!this.sleepModeEnabled) {
                // 睡眠模式关闭：删除现有定时器
                actionValue = 2;
            } else if (this.hasExistingTimer) {
                // 睡眠模式开启且存在现有定时器：更新定时器
                actionValue = 0;
            } else {
                // 睡眠模式开启且不存在现有定时器：创建新定时器
                actionValue = 1;
                console.log('➕ 创建新定时器 (action=1)');
            }

            // 如果睡眠模式开启，验证时间设置
            if (this.sleepModeEnabled) {
                const hasStartTime = !this.isTimeNotSet(this.startIndex);
                const hasEndTime = !this.isTimeNotSet(this.endIndex);

                if (!hasStartTime && !hasEndTime) {
                    throw new Error("请至少设置开启时间或关闭时间");
                }
            }

            // 获取UTC格式的时间字符串
            const startTimeUTC = this.convertTimeToUTCString(this.startIndex);
            const endTimeUTC = this.convertTimeToUTCString(this.endIndex);
            const weekValue = this.generateWeekString();

            // 构建定时器命令数据（只包含sleepMode，移除告警灯相关）
            const timerData = {
                action: actionValue,  // 根据逻辑确定的action值
                timer: [{
                    id: 1,
                    enable: this.sleepModeEnabled ? 1 : 0,  // 根据睡眠模式状态设置enable
                    start: startTimeUTC,                // 开始时间UTC格式 (HHmmssZ)
                    end: endTimeUTC,                    // 结束时间UTC格式 (HHmmssZ)
                    week: weekValue,                    // 重复星期设置 (0表示一次性闹钟)
                    sid: 'sleepMode',                   // 执行服务ID - 只保留睡眠模式
                    para: 'on',                         // 服务属性名
                    paraValue: this.sleepModeEnabled ? 1 : 0,  // 睡眠模式状态
                }]
            };

            console.log('🌙 睡眠模式定时器下发数据:', timerData);
            console.log('🌙 定时器参数详情:', {
                action值: actionValue,
                action含义: actionValue === 0 ? '更新' : actionValue === 1 ? '创建' : '删除',
                睡眠模式: this.sleepModeEnabled ? '开启' : '关闭',
                存在现有定时器: this.hasExistingTimer,
                定时器启用: timerData.timer[0].enable === 1 ? '启用' : '禁用',
                本地开始时间: this.formatTimeDisplay(this.startIndex),
                本地结束时间: this.formatTimeDisplay(this.endIndex),
                开始时间UTC: timerData.timer[0].start || '未设置',
                结束时间UTC: timerData.timer[0].end || '未设置',
                重复设置: timerData.timer[0].week,
                重复类型: timerData.timer[0].week === 0 ? '一次性闹钟' : '重复闹钟',
                重复二进制: timerData.timer[0].week.toString(2).padStart(7, '0'),
                服务ID: timerData.timer[0].sid,
                属性: timerData.timer[0].para,
                睡眠模式状态值: timerData.timer[0].paraValue,
                时区转换: '本地时间-8小时=UTC时间',
                时间验证: {
                    开始时间有效: startTimeUTC !== "",
                    结束时间有效: endTimeUTC !== "",
                    至少一个时间: startTimeUTC !== "" || endTimeUTC !== ""
                }
            });

            // 下发命令到设备 - 使用正确的数据格式
            const commandData = {
                timer: timerData  // 直接使用timer作为服务ID
            };

            console.log('🌙 最终定时器下发数据:', JSON.stringify(commandData, null, 2));

            return this.$store.dispatch("setDevInfo", commandData)
                .then(() => {
                    // 睡眠模式定时器保存成功

                    // 更新本地状态
                    if (actionValue === 2) {
                        // 删除定时器后，标记为无现有定时器
                        this.hasExistingTimer = false;
                    } else if (actionValue === 1) {
                        // 创建定时器后，标记为有现有定时器
                        this.hasExistingTimer = true;
                    }
                })
                .catch((error) => {
                    console.error("❌ 睡眠模式定时器设置失败:", error);
                    throw error; // 重新抛出错误，让上层处理
                });
        },





        // 初始化重复选项
        initializeRepeatOptions () {
            this.repeatOptions = [
                { name: this.$t("run_once"), value: 0 },
                { name: this.$t("week_repeat"), value: 1 },
                { name: this.$t("weekend_repeat"), value: 2 },
                { name: this.$t("daily_repeat"), value: 3 },
                { name: this.$t("custom_repeat"), value: 4 },
            ];
        },

        // 初始化数据
        initializeData () {
            console.log('🔍 initializeData - Timer数据:', JSON.stringify(this.Timer));

            // 从store获取告警灯状态
            if (this.IndicatorOn !== undefined) {
                this.alarmLightEnabled = this.IndicatorOn == 1;
                console.log('💡 initializeData - 从store获取告警灯状态:', this.alarmLightEnabled);
            } else {
                console.log('💡 initializeData - 使用告警灯默认状态:', this.alarmLightEnabled);
            }
            // 从Timer获取睡眠模式相关数据
            if (this.Timer && this.Timer.timer && Array.isArray(this.Timer.timer) && this.Timer.timer.length > 0) {
                const timerItem = this.Timer.timer[0];
                console.log('🔍 initializeData - timerItem:', timerItem);

                this.sleepModeEnabled = timerItem.enable == 1;

                // 标记存在现有定时器
                this.hasExistingTimer = true;
                // 解析开始时间
                const startTimeIndex = this.parseTimeValue(timerItem.start);
                this.startIndex = startTimeIndex !== null ? startTimeIndex : null;
                console.log('🔍 initializeData - 设置startIndex:', this.startIndex);

                // 解析结束时间
                const endTimeIndex = this.parseTimeValue(timerItem.end);
                this.endIndex = endTimeIndex !== null ? endTimeIndex : null;
                console.log('🔍 initializeData - 设置endIndex:', this.endIndex);

                // 解析重复设置
                const week = timerItem.week || 0;
                this.parseWeekToRepeatIndex(week);
                console.log('🔍 initializeData - 设置repeatIndex:', this.repeatIndex);
            } else {
                this.hasExistingTimer = false;

                // 使用初始化的默认状态（睡眠模式相关）
                this.sleepModeEnabled = false;  // 初始化：关闭睡眠模式
                this.startIndex = null;         // 初始化：开始时间未设置
                this.endIndex = null;           // 初始化：结束时间未设置
                this.repeatIndex = 0;           // 初始化：不重复
                this.customWeekDays = [];       // 初始化：清空自定义重复天数

                console.log('🔄 initializeData - 使用初始化状态:', {
                    睡眠模式: this.sleepModeEnabled,
                    告警灯: this.alarmLightEnabled, // 告警灯状态使用data中的默认值
                    开始时间索引: this.startIndex,
                    结束时间索引: this.endIndex,
                    重复索引: this.repeatIndex,
                    自定义重复天数: this.customWeekDays
                });
            }

            console.log('✅ initializeData 完成:', {
                睡眠模式: this.sleepModeEnabled,
                告警灯状态: this.alarmLightEnabled,
                存在现有定时器: this.hasExistingTimer,
                开始时间索引: this.startIndex,
                结束时间索引: this.endIndex,
                重复索引: this.repeatIndex
            });
        },

        // 解析周重复值到重复索引（支持数值和字符串格式）
        parseWeekToRepeatIndex (weekValue) {
            console.log('🔍 parseWeekToRepeatIndex 输入:', weekValue, typeof weekValue);

            let newIndex = 0;
            let numericValue = weekValue;

            // 如果是字符串格式，转换为数值
            if (typeof weekValue === 'string') {
                if (weekValue.length === 7) {
                    // 二进制字符串格式 "1111100"
                    numericValue = parseInt(weekValue, 2);
                    console.log('📝 二进制字符串转换:', weekValue, '->', numericValue);
                } else {
                    // 数字字符串格式
                    numericValue = parseInt(weekValue);
                    console.log('📝 数字字符串转换:', weekValue, '->', numericValue);
                }
            }

            console.log('🔢 转换后的数值:', numericValue);

            // 根据数值判断重复类型
            switch (numericValue) {
                case 0:
                    newIndex = 0; // 执行一次
                    break;
                case 31:  // 11111 (周一到周五)
                    newIndex = 1; // 工作日重复
                    break;
                case 96:  // 1100000 (周六周日)
                    newIndex = 2; // 周末重复
                    break;
                case 127: // 1111111 (全部7天)
                    newIndex = 3; // 每日重复
                    break;
                default:
                    newIndex = 4; // 自定义重复
                    // 解析自定义选择的星期几
                    this.customWeekDays = [];
                    for (let i = 0; i < 7; i++) {
                        if (numericValue & (1 << i)) {
                            this.customWeekDays.push(i);
                        }
                    }
                    console.log('🎯 自定义重复解析:', this.customWeekDays);
                    break;
            }

            console.log('⚙️ 设置前 repeatIndex:', this.repeatIndex);
            this.repeatIndex = newIndex;
            console.log('✅ 设置后 repeatIndex:', this.repeatIndex);
            console.log(`🎉 解析周重复完成: 输入值=${weekValue}, 数值=${numericValue}, 索引=${newIndex}, 自定义天数=${JSON.stringify(this.customWeekDays)}`);
        },

        // 将时间索引转换为UTC时间格式 (HHmmssZ) - 减去8小时时区偏移
        convertTimeToUTCString (timeIndex) {
            if (this.isTimeNotSet(timeIndex)) {
                return "";  // 未设置时返回空字符串
            }

            // 本地时间转UTC时间：减去8小时（480分钟）
            let utcTimeIndex = timeIndex - 480;

            // 处理跨日情况
            if (utcTimeIndex < 0) {
                utcTimeIndex += 1440;  // 加上24小时（1440分钟）
            } else if (utcTimeIndex >= 1440) {
                utcTimeIndex -= 1440;  // 减去24小时（1440分钟）
            }

            const hour = Math.floor(utcTimeIndex / 60);
            const minute = utcTimeIndex % 60;
            const second = 0;  // 秒数固定为0

            console.log('🌍 时区转换:', {
                本地时间索引: timeIndex,
                本地时间显示: this.formatTimeDisplay(timeIndex),
                UTC时间索引: utcTimeIndex,
                UTC时间显示: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                时区偏移: '-8小时'
            });

            return `${hour.toString().padStart(2, '0')}${minute.toString().padStart(2, '0')}${second.toString().padStart(2, '0')}Z`;
        },

        // 将UTC时间格式转换为时间索引 (HHmmssZ -> 分钟数)
        convertUTCStringToTimeIndex (utcTimeString) {
            if (!utcTimeString || utcTimeString === "") {
                return null;  // 未设置
            }

            console.log('🔍 解析UTC时间字符串:', utcTimeString);

            // 移除末尾的Z
            const timeStr = utcTimeString.replace('Z', '');
            console.log('🔍 移除Z后的时间字符串:', timeStr, '长度:', timeStr.length);

            // UTC格式应该是6位数字 (HHmmss) 或至少4位 (HHmm)
            if (timeStr.length >= 4) {
                const hour = parseInt(timeStr.substring(0, 2), 10);
                const minute = parseInt(timeStr.substring(2, 4), 10);

                console.log('🔍 解析出的时间:', { hour, minute });

                if (!isNaN(hour) && !isNaN(minute) && hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
                    // UTC时间转本地时间：加上8小时（480分钟）
                    let utcTimeIndex = hour * 60 + minute;
                    let localTimeIndex = utcTimeIndex + 480;

                    // 处理跨日情况
                    if (localTimeIndex >= 1440) {
                        localTimeIndex -= 1440;  // 减去24小时（1440分钟）
                    } else if (localTimeIndex < 0) {
                        localTimeIndex += 1440;  // 加上24小时（1440分钟）
                    }

                    console.log('🌍 UTC时间解析和时区转换:', {
                        UTC时间显示: `${hour}:${minute}`,
                        UTC时间索引: utcTimeIndex,
                        本地时间索引: localTimeIndex,
                        本地时间显示: this.formatTimeDisplay(localTimeIndex),
                        时区偏移: '+8小时'
                    });

                    return localTimeIndex;
                } else {
                    console.warn('⚠️ 时间值超出有效范围:', { hour, minute });
                }
            } else {
                console.warn('⚠️ UTC时间字符串长度不足:', timeStr.length);
            }

            console.error('❌ UTC时间解析失败:', utcTimeString);
            return null;
        },

        // 生成周重复字符串 (7位二进制字符串，对应周一到周日)
        generateWeekString () {
            console.log('🔄 generateWeekString 输入 repeatIndex:', this.repeatIndex);
            console.log('🔄 customWeekDays:', JSON.stringify(this.customWeekDays));

            let result;
            switch (this.repeatIndex) {
                case 0:
                    result = 0;        // 执行一次 - 使用数值0
                    break;
                case 1:
                    result = 31;       // 工作日重复 - 周一到周五 (11111 = 31)
                    break;
                case 2:
                    result = 96;       // 周末重复 - 周六周日 (1100000 = 96)
                    break;
                case 3:
                    result = 127;      // 每日重复 - 全部7天 (1111111 = 127)
                    break;
                case 4: {
                    // 自定义重复 - 根据选择的天数计算
                    let weekValue = 0;
                    this.customWeekDays.forEach(dayIndex => {
                        weekValue |= (1 << dayIndex);  // 位运算设置对应位
                        console.log(`🎯 设置第${dayIndex}天, 当前值: ${weekValue}`);
                    });
                    result = weekValue;
                    break;
                }
                default:
                    result = 0;      // 默认执行一次
                    break;
            }


            return result;
        },

        // 将周重复数值转换为二进制字符串（用于显示和调试）
        weekValueToBinaryString (weekValue) {
            return weekValue.toString(2).padStart(7, '0');
        },

        // 解析时间值 - 支持UTC格式(HHmmssZ)、数值(分钟)和字符串(HH:MM)格式
        parseTimeValue (timeValue) {
            console.log('🔍 parseTimeValue 输入:', timeValue, '类型:', typeof timeValue);

            if (timeValue == null || timeValue === undefined || timeValue === '') {
                return null;  // 返回null表示未设置
            }

            // 如果是数值，直接返回（假设是分钟数）
            if (typeof timeValue === 'number') {
                console.log('✅ 数值类型时间:', timeValue);
                return timeValue;
            }

            if (typeof timeValue === 'string') {
                // 如果是UTC格式 "HHmmssZ"
                if (timeValue.endsWith('Z') && timeValue.length >= 5) {
                    console.log('🔍 检测到UTC格式时间:', timeValue);
                    const result = this.convertUTCStringToTimeIndex(timeValue);
                    console.log('🔍 UTC格式解析结果:', result);
                    return result;
                }

                // 如果是字符串格式 "HH:MM"
                if (timeValue.includes(':')) {
                    console.log('🔍 检测到HH:MM格式时间:', timeValue);
                    const [hours, minutes] = timeValue.split(':').map(num => parseInt(num, 10));
                    if (!isNaN(hours) && !isNaN(minutes)) {
                        const result = hours * 60 + minutes;
                        console.log('✅ HH:MM格式解析成功:', result);
                        return result;
                    }
                }

                // 如果是纯数字字符串
                const numValue = parseInt(timeValue, 10);
                if (!isNaN(numValue)) {
                    console.log('✅ 数字字符串解析成功:', numValue);
                    return numValue;
                }
            }

            console.error('❌ 时间值解析失败:', timeValue);
            return null;  // 无法解析时返回null
        },
    },
};
</script>
<style lang="less" scoped>
@import url("../../lib/style/public.less");

#sleep-page {
    .content {
        // pad适配
        margin: 0 var(--second_margin) 0px var(--second_margin);
        position: relative;
    }

    .module-box {
        margin: 0.8rem 0.6rem 0;
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
    }

    .class-mode-item {
        // flex: 1;
        width: calc(100% - 3.2rem);
        background-color: var(--emui_card_panel_bg);
        border-radius: var(--emui_corner_radius_large);
        min-height: 4rem;
        padding: 0 1.2rem;
        margin: 0.4rem 0.6rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--emui_text_primary);

        .left {
            flex: 1;
            margin-right: 1.6rem;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;

            &>span:nth-child(1) {
                font-size: 1.6rem;
                color: var(--emui_text_primary);
                line-height: 2.2rem;
            }

            &>span:nth-child(2) {
                font-size: 1.4rem;
                line-height: 1.8rem;
                color: var(--emui_text_secondary);
            }
        }
    }

    .class-mode-tip {
        margin: 0.8rem 1.8rem 0;
        font-size: 1.2rem;
        color: var(--emui_text_secondary);
        line-height: 1.6rem;
    }

    // 保存按钮容器
    .save-button-container {
        margin-top: 2.4rem;
        padding: 0 1.2rem;

        .save-button {
            width: 100%;
            height: 4rem;
            background-color: var(--emui_accent);
            color: var(--emui_text_primary_inverse);
            font-size: var(--emui_text_size_button1);
            font-weight: 500;
            border-radius: 2rem;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
    }
}
</style>
