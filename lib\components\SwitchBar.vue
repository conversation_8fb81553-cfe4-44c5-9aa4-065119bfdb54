<template>
  <div :id="idStr !== '' ? 'com.huawei.smarthome:id/switch_bar_' + idStr : ''" ref="controlRef" class="control-bar-container" :class="{ 'inside': inside }"
    @touchstart="handlerTouchstart" @touchmove="handlerTouchmove" @touchend="handlerTouchend">
    <div :class="['control-bar', (disabled && !inside) ? 'disabled' : '']" ref="control-bar">
      <div class="left" ref="modeTitle">
        <div class="title-icon">
          <div class="title" :style="{ fontSize: fontSize, width: titleWidth}">
            <span ref="name">{{ name }}</span>
          </div>
          <div v-if="tip" class="icon-box">
            <div :class="showTipRight ? 'mod-tips-right' : 'mod-tips'" v-if="showTip">
              <div class="content">{{ tip }}</div>
              <div class="triangle"></div>
            </div> 
            <img  :id="idStr !== '' ? 'com.huawei.smarthome:id/switch_tips_' + idStr : ''" ref="iconRef" class="icon" @click.stop="handlerShowTip" :src="tipIcon" alt="">
          </div>
        </div>
        <div class="info" v-show="info && active" :style="{ fontSize: subFontSize }">{{ info }}</div>
      </div>
      <div :id="idStr !== '' ? 'com.huawei.smarthome:id/switch_' + idStr : ''" class="right" @click.stop="handleClick">
        <HCSwitch v-model="active" :disabled="disabled"></HCSwitch>
      </div>
      <div class="divider" v-if="divider"></div>
    </div>
  </div>
</template>

<script>
import HCSwitch from './HCSwitch';

export default {
  name: 'SwitchBar',
  components: {
    HCSwitch
  },
  props: {
    name: {
      default: 'Name',
      type: String
    },
    info: {
      default: null,
      type: String
    },
    active: {
      default: false,
      type: Boolean
    },
    disabled: {
      default: false,
      type: Boolean
    },
    inside: {
      type: Boolean,
      default: false
    },
    divider: {
      type: Boolean,
      default: false
    },
    tip: {
      default: null,
      type: String
    },
    idStr: {
      default: '',
      type: String
    }
  },
  data() {
    return {
      fontSize: '1.6rem',
      subFontSize: '1.2rem',
      isLongPress: false,
      showTipRight: false,
      showTip: false,
      titleWidth: 'auto'
    };
  },
  watch: {
    name: {
      handler(val) {
        setTimeout(() => {
          this.updateWidth()
          this.titleWidth =  'auto'
          let divWidth = this.$refs.modeTitle.getBoundingClientRect().width;
          for (var i = 0; i < 5; i++) {
            let textWidth = this.measureText(this.name, 1.6 - i * 0.1).width;
            if (textWidth <= divWidth) {
              this.fontSize = (1.6 - i * 0.1) + 'rem';
              return;
            }
          }
          this.fontSize = '1.2rem';
        }, 40)
      },
      immediate: true
    },
    active: {
      handler(val) {
        if (!val) return
        setTimeout(() => {
          let divWidth = this.$refs.modeTitle.getBoundingClientRect().width;
          for (var i = 0; i < 4; i++) {
            let textWidth = this.measureText(this.info, 1.2 - i * 0.1).width;
            if (textWidth <= divWidth) {
              this.subFontSize = (1.2 - i * 0.1) + 'rem';
              return;
            }
          }
          this.subFontSize = '0.9rem';
        }, 40)
      },
      immediate: true
    }
  },
  computed: {
    tipIcon() {
      return require(`../../lib/assets/${this.$store.getters.imgPath}ic_tip.png`)
    }
  },
  mounted() {
    if (document.body.clientWidth / this.$refs.controlRef.offsetLeft > 2) {
      this.showTipRight = false
    } else {
      this.showTipRight = true
    }
    let self = this
    document.body.addEventListener('touchstart', function (e) {
      if (self.showTip) {
        self.showTip = false
      }
    })
  },
  methods: {
    measureText(pText, pFontSize) {
      var lDiv = document.createElement('div');
      document.body.appendChild(lDiv);
      lDiv.style.fontSize = pFontSize + 'rem';
      lDiv.style.position = 'absolute';
      lDiv.style.left = -1000;
      lDiv.style.top = -1000;
      lDiv.innerHTML = pText;
      var lResult = {
        width: lDiv.clientWidth,
        height: lDiv.clientHeight
      };
      document.body.removeChild(lDiv);
      lDiv = null;
      return lResult;
    },
    handleClick(e) {
      if (!this.disabled) {
        let top = e.clientY < 350 ? 'bottom' : 'top';
        this.$emit('handleClick', top);
      }
    },
    handlerShowTip() {
      this.showTip = true
    },
    handlerTouchstart(e) {
      this.isLongPress = false
      if (this.timeOut) {
        clearTimeout(this.timeOut)
        this.timeOut = null
      }
      const self = this
      this.timeOut = setTimeout(function () {
        self.isLongPress = true
      }, 500)
    },
    handlerTouchmove() {
      if (this.timeOut) {
        clearTimeout(this.timeOut)
        this.timeOut = null
      }
      this.isLongPress = false
    },
    handlerTouchend(e) {
      if (this.isLongPress) {
        this.$emit('longPress');
        e.preventDefault()
      }
      if (this.timeOut) {
        clearTimeout(this.timeOut)
        this.timeOut = null
      }
    },
    showTipInfo() {
      this.showTip = true
    },
    updateWidth() {
      setTimeout(() => {
        this.titleWidth = this.$refs.name.getBoundingClientRect().width + 1 + 'px'
      }, 100)
    }
  },
  beforeDestroy() {
    if (this.timeOut) {
      clearTimeout(this.timeOut)
      this.timeOut = null
    }
    document.body.removeEventListener('touchstart')
  }
};
</script>

<style lang="less" scoped>
@import url('../style/public.less');

.control-bar-container {
  padding: 0 .6rem 1.2rem;
  width: 50%;
  box-sizing: border-box;
  position: relative;

  &.inside {
    padding: 0px;

    .control-bar {
      padding: 0px 0.8rem;
    }
  }

  .control-bar {
    flex: 1;
    height: 6.4rem;
    .cardStyle();
    margin: 0;
    display: flex;
    justify-content: space-between;
    color: var(--emui_text_primary);

    &.disabled {
      opacity: .4;
    }

    .divider {
      position: absolute;
      width: calc(100% - 1.6rem);
      height: 1px;
      bottom: 0;
      left: 0.8rem;
      transform: scaleY(0.25);
      background: var(--emui_color_divider_horizontal);
    }

    .left {
      flex: 1;
      width: 0;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .title-icon {
        display: flex;
        flex-direction: row;
        align-items: center;

        .title {
          font-size: 1.6rem;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden; //溢出内容隐藏
          text-overflow: ellipsis; //文本溢出部分用省略号表示
          display: -webkit-box; //特别显示模式
          -webkit-line-clamp: 2; //行数
          line-clamp: 2;
          -webkit-box-orient: vertical; //盒子中内容竖直排列
          word-break: break-word;
        }

        .icon-box {
          width: 1.6rem;
          height: 1.6rem;
          position: relative;

          .icon {
            margin-left: 0.4rem;
            width: 1.6rem;
            height: 1.6rem;
          }
        }
      }

      .info {
        margin-top: 0.3rem;
        font-size: 1.2rem;
        white-space: nowrap;
        color: var(--emui_text_secondary);
        text-overflow: ellipsis;
        overflow: hidden;
        max-lines: 1;
      }
    }

    .right {
      display: flex;
      margin-left: 0.8rem;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      width: 3.6rem;
    }
  }
}
</style>
