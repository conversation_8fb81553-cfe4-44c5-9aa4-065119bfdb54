<template>
    <header :style="{
                paddingTop: `${statusBarHeight}px`,
                backgroundColor
            }">
        <!-- 左侧按钮 -->
        <button  id="com.huawei.smarthome:id/title-bar-back" class="icon left"
                :style="{
                    backgroundImage:`url(${leftIconUrl})`,
                    opacity: disabled ? '.4' : ''
                }"
                @click="$emit('leftClick')"></button>
        <!-- 中间标题 -->
        <div class="title-container">
            <!-- 主标题 -->
            <h1 class="title"
                :style="{color: titleColor}">{{ title }}</h1>
            <!-- 副标题 -->
            <h2 v-if="subTitle"
                class="subtitle"
                :style="{color: subTitleColor}">{{ subTitle }}</h2>
        </div>
        <!-- 右侧按钮 -->
        <button
            id="com.huawei.smarthome:id/title-bar-more"
            class="icon right" v-if="showRightIcon"
            :style="{backgroundImage:`url(${rightIconUrl})`}"
            @click="$emit('rightClick')"></button>
        <slot></slot>
    </header>
</template>

<script>

export default {
    name: "Titlebar",
    props: {
        // 主标题
        title: {
            default: '',
            type: String
        },
        titleColor: {
            default: '',
            type: String
        },
        // 副标题
        subTitle: {
            default: '',
            type: String
        },
        subTitleColor: {
            default: '',
            type: String
        },
        // 左侧图标
        leftIcon: {
            default: '',
            type: String
        },
        // 右侧图标
        rightIcon: {
            default: '',
            type: String
        },
        // 右侧图标
        showRightIcon: {
            default: true,
            type: Boolean
        },
        backgroundColor: {
            type: String
        },
        disabled: {
            default: false,
            type: Boolean
        }
    },
    computed: {
        leftIconUrl() {
            if (!this.leftIcon && this.leftIcon !== null) {
                return require(`../assets/${this.$store.getters.imgPath}ic_back.png`);
            }
            return this.leftIcon;
        },
        rightIconUrl() {
            if (!this.rightIcon && this.rightIcon !== null) {
                return require(`../assets/${this.$store.getters.imgPath}ic_more.png`);
            }
            return this.rightIcon;
        },
        statusBarHeight() {
            return this.$store.getters.statusBarHeight
        }
    }
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

header {
    width: 100%;
    height: 5.6rem;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    display: flex;
    align-items: center;
    overflow: hidden;
    background-color: var(--emui_color_subbg);

    .title-container {
        flex: 1;
        text-align: left;
        overflow: hidden;
        z-index: 10;

        .title {
            width: 100%;
            font-size: 2.0rem;
            color: var(--emui_text_primary);
            font-weight: normal;
            .medium();
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .subtitle {
            font-size: 1.4rem;
            color: var(--emui_text_secondary);
            font-weight: normal;
        }
    }

    .icon {
        width: 2.4rem;
        height: 2.4rem;
        padding: 0 1.2rem;
        margin: 0.4rem;
        background-size: 2.4rem;
        background-repeat: no-repeat;
        box-sizing: content-box;
        background-position: center;
        z-index: 10;

        &.left {
            margin-left: 1.2rem;
        }

        &.right {
            margin-right: 0.4rem;
        }

        &:active {
            border-radius: 0.8px;
            background-color: var(--emui_clickeffic_default_color);
        }
    }
}
</style>
