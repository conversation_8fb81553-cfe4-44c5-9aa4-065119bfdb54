<template>
  <div class="gauge-chart">
    <canvas ref="gaugeCanvas"
            class="gauge-canvas"
            :width="width"
            :height="height">
    </canvas>
    <div v-if="showText" class="gauge-text-container" :style="showSubText ? 'top: 50%' : 'top: 55%'">
      <div class="gauge-text-main"
           :style="{ color: mainTextColor }">
        {{ displayMainText }}
      </div>
      <div v-if="showSubText && subText"
           class="gauge-text-sub">
        {{ subText }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "GaugeChart",
  props: {
    // 画布尺寸
    width: {
      type: Number,
      default: 320
    },
    height: {
      type: Number,
      default: 300
    },
    // 当前值 (3: 差, 2: 良, 1: 优)
    value: {
      type: Number,
      default: 1,
      validator: (value) => [0, 1, 2, 3].includes(value)
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否显示中心文字
    showText: {
      type: Boolean,
      default: true
    },
    // 主文字内容（如果不传，会根据 value 自动显示优良差）
    textContent: {
      type: String,
      default: ''
    },
    // 是否显示副文字
    showSubText: {
      type: Boolean,
      default: false
    },
    // 副文字内容
    subText: {
      type: String,
      default: ''
    },
    // 自定义主文字颜色（如果不传，会根据 value 自动设置颜色）
    textColor: {
      type: String,
      default: ''
    },
    // 自定义颜色配置
    customColors: {
      type: Object,
      default: null
    }
  },
  computed: {
    // 渐变色配置（支持暗黑模式）
    gaugeColors() {
      if (this.customColors) {
        return this.customColors;
      }
      
      if (this.$store.state.isDarkMode) {
        return {
          // 暗黑模式颜色
          greenLight: '#4A7C59',
          greenMid: '#5BA854',
          greenDeep: '#5BA854',
          yellowLight: '#E8D547',
          yellowMid: '#DCC441',
          yellowDeep: '#D1A738',
          redDeep: '#D94E38',
          redMid: '#D94E38',
          redLight: '#E84026'
        };
      } else {
        return {
          // 浅色模式颜色
          greenLight: '#4CAF50',
          greenMid: '#64BB5C',
          greenDeep: '#64BB5C',
          yellowLight: '#FFF176',
          yellowMid: '#FFEB3B',
          yellowDeep: '#F7CE00',
          redDeep: '#E84026',
          redMid: '#E84026',
          redLight: '#D32F2F'
        };
      }
    },
    // 仪表盘颜色
    gaugeColor() {
      switch (this.value) {
        case 1: return this.gaugeColors.greenDeep; // 绿色 - 优
        case 2: return this.gaugeColors.yellowDeep; // 黄色 - 良
        case 3: return this.gaugeColors.redDeep; // 红色 - 差
        default: return '#CCCCCC';
      }
    },
    // 显示的主文字内容
    displayMainText() {
      if (this.textContent) {
        return this.textContent;
      }

      if (this.disabled) return '--';

      // 根据 value 自动显示对应文字（需要国际化支持）
      switch (this.value) {
        case 1: return this.$t ? this.$t('quality_excellent') : '优';
        case 2: return this.$t ? this.$t('quality_good') : '良';
        case 3: return this.$t ? this.$t('quality_poor') : '差';
        default: return '--';
      }
    },
    // 主文字颜色
    mainTextColor() {
      if (this.textColor) {
        return this.textColor;
      }

      if (this.disabled) return 'var(--emui_text_primary)';

      // 根据 value 自动设置颜色
      switch (this.value) {
        case 1: return this.gaugeColors.greenDeep; // 绿色 - 优
        case 2: return this.gaugeColors.yellowDeep; // 黄色 - 良
        case 3: return this.gaugeColors.redDeep; // 红色 - 差
        default: return 'var(--emui_text_primary)';
      }
    }
  },
  mounted() {
    this.drawGauge();
  },
  watch: {
    value() {
      this.drawGauge();
    },
    disabled() {
      this.drawGauge();
    }
  },
  methods: {
    drawGauge() {
      const canvas = this.$refs.gaugeCanvas;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      const centerX = this.width / 2;
      const centerY = this.height / 2 + 10;
      const radius = Math.min(this.width, this.height) * 0.35; // 相对于画布大小的比例
      const lineWidth = Math.min(this.width, this.height) * 0.1; // 相对线宽

      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 真正的270度弧线：从135度开始，顺时针270度到45度
      const startAngle = Math.PI * 0.75;  // 135度
      const endAngle = Math.PI * 0.25;    // 45度

      // 绘制背景弧线（270度弧线）
      ctx.beginPath();
      // 从135度开始，顺时针绘制270度到45度
      // 需要分两段绘制：135度到360度，0度到45度
      ctx.arc(centerX, centerY, radius, startAngle, Math.PI * 2, false);
      ctx.arc(centerX, centerY, radius, 0, endAngle, false);
      ctx.strokeStyle = '#f0f0f0';
      ctx.lineWidth = lineWidth;
      ctx.lineCap = 'round';
      ctx.stroke();

      // 绘制彩色弧线段
      this.drawColoredArc(ctx, centerX, centerY, radius, lineWidth, startAngle, endAngle);

      // 绘制指示器
      this.drawIndicator(ctx, centerX, centerY, radius);
    },
    drawColoredArc(ctx, centerX, centerY, radius, lineWidth, startAngle, endAngle) {
      // 分段绘制弧形渐变
      const segments = 54; // 270度分成54段，每段5度
      const totalAngle = Math.PI * 1.5; // 270度 (从startAngle到endAngle)
      const segmentAngle = totalAngle / segments;

      for (let i = 0; i < segments; i++) {
        const progress = i / (segments - 1); // 0 到 1 的进度
        const color = this.interpolateColor(progress);

        const currentAngle = startAngle + segmentAngle * i;
        const nextAngle = startAngle + segmentAngle * (i + 1);

        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, currentAngle, nextAngle, false);
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;
        ctx.lineCap = 'round';
        ctx.stroke();
      }
    },
    // 颜色插值函数
    interpolateColor(progress) {
      // 根据进度计算颜色，增强黄色区域的过渡
      // 0% 绿色最深 -> 20% 绿色浅 -> 35% 黄色浅 -> 50% 黄色最深 -> 65% 黄色中 -> 80% 红色浅 -> 100% 红色最深

      if (progress <= 0.2) {
        // 0% - 20%: 绿色最深 -> 绿色浅
        return this.lerpColor(this.gaugeColors.greenDeep, this.gaugeColors.greenLight, progress / 0.2);
      } else if (progress <= 0.35) {
        // 20% - 35%: 绿色浅 -> 黄色浅
        return this.lerpColor(this.gaugeColors.greenLight, this.gaugeColors.yellowLight, (progress - 0.2) / 0.15);
      } else if (progress <= 0.5) {
        // 35% - 50%: 黄色浅 -> 黄色最深
        return this.lerpColor(this.gaugeColors.yellowLight, this.gaugeColors.yellowDeep, (progress - 0.35) / 0.15);
      } else if (progress <= 0.65) {
        // 50% - 65%: 黄色最深 -> 黄色中
        return this.lerpColor(this.gaugeColors.yellowDeep, this.gaugeColors.yellowMid, (progress - 0.5) / 0.15);
      } else if (progress <= 0.8) {
        // 65% - 80%: 黄色中 -> 红色浅
        return this.lerpColor(this.gaugeColors.yellowMid, this.gaugeColors.redLight, (progress - 0.65) / 0.15);
      } else {
        // 80% - 100%: 红色浅 -> 红色最深
        return this.lerpColor(this.gaugeColors.redLight, this.gaugeColors.redDeep, (progress - 0.8) / 0.2);
      }
    },
    // 线性插值函数
    lerpColor(color1, color2, t) {
      const rgb1 = this.hexToRgb(color1);
      const rgb2 = this.hexToRgb(color2);

      const r = Math.round(rgb1.r + (rgb2.r - rgb1.r) * t);
      const g = Math.round(rgb1.g + (rgb2.g - rgb1.g) * t);
      const b = Math.round(rgb1.b + (rgb2.b - rgb1.b) * t);

      return `rgb(${r}, ${g}, ${b})`;
    },
    // 十六进制转RGB
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    drawIndicator(ctx, centerX, centerY, radius) {
      if (this.disabled) return;

      // 计算指示器位置
      const angle = this.getIndicatorAngle();
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      // 绘制阴影
      ctx.beginPath();
      ctx.arc(x + 1, y + 2, 6, 0, Math.PI * 2);
      ctx.fillStyle = 'rgba(0,0,0,0.2)';
      ctx.fill();

      // 绘制指示器
      ctx.beginPath();
      ctx.arc(x, y, 12, 0, Math.PI * 2);
      ctx.fillStyle = this.gaugeColor;
      ctx.fill();

      // 绘制白色边框
      ctx.beginPath();
      ctx.arc(x, y, 12, 0, Math.PI * 2);
      ctx.strokeStyle = 'white';
      ctx.lineWidth = 4;
      ctx.stroke();
    },
    getIndicatorAngle() {
      const startAngle = Math.PI * 1.25; // 225度
      const totalAngle = Math.PI * 0.5;   // 90度 (315-225)

      switch (this.value) {
        case 1: return startAngle - totalAngle * 0.8; 
        case 2: return startAngle + totalAngle * 0.5;
        case 3: return startAngle + totalAngle * 1.5;  
        default: return startAngle;
      }
    }
  }
};
</script>

<style lang='less' scoped>
.gauge-chart {
  position: relative;
  display: inline-block;

  .gauge-canvas {
    width: 100%;
    height: 100%;
  }

  .gauge-text-container {
    position: absolute;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
    left: 50%;
    .gauge-text-main {
      font-size: 2.4rem;
      font-weight: 600;
      transition: color 0.3s ease;
      line-height: 1.2;
    }

    .gauge-text-sub {
      font-size: 1.2rem;
      color: var(--emui_text_secondary);
      margin-top: 0.8rem;
      line-height: 1.3;
    }
  }
}
</style>
