<template>
  <div class="dialog" @click="cancel" v-if="value">
    <div class="dialog-container">
      <h3 class="title">{{ title }}</h3>
      <p class="message" v-html="message"></p>
      <div class="dialog-btns">
        <div @click="cancel" :class="[ cancelButtonClasses ]">
          <p>{{ cancelButtonText }}</p>
        </div>
        <span class="line"></span>
        <div @click='confirm' :class="[ confirmButtonClasses ]">
          <p>{{ confirmButtonText }}</p>
        </div>
      </div>
    </div>
  </div>

</template>
<script>
let CONFIRM_TEXT = '确定';
let CANCEL_TEXT = '取消';

export default {
  name: 'MessageBox',
  methods: {
    cancel() {
      this.value = false
      this.callback('cancel');
    },
    confirm() {
      this.value = false
      this.callback('confirm');
    }
  },
  data() {
    return {
      callback: null,
      value: false,
      title: '',
      message: '',
      type: '',
      showConfirmButton: true,
      showCancelButton: false,
      confirmButtonText: CONFIRM_TEXT,
      cancelButtonText: CANCEL_TEXT,
      confirmButtonClass: '',
      confirmButtonDisabled: false,
      cancelButtonClass: '',
    };
  },
  computed: {
    confirmButtonClasses() {
      let classes = (this.confirmButtonClass && this.confirmButtonClass.length > 0) ? this.confirmButtonClass : '';
      return classes;
    },
    cancelButtonClasses() {
      let classes = (this.cancelButtonClass && this.cancelButtonClass.length > 0) ? this.cancelButtonClass : '';
      return classes;
    }
  },

};
</script>

<style lang="less" scoped>
@import url("../../style/public.less");

// 自定义MessageBox样式
.dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-container {
  position: relative !important;
  bottom: auto !important;
  left: auto !important;
  right: auto !important;
  width: calc(100% - 2.4rem);
  max-width: 32rem;
  animation: dialogFadeIn 300ms ease-in-out 0ms !important;

  .title {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow: visible !important;
    text-overflow: unset !important;
    height: auto !important;
    line-height: 2.6rem !important;
    padding: 1.5rem 2.4rem 0.8rem 2.4rem !important;
  }

  .message {
    font-size: 1.4rem !important;
    color: var(--emui_text_secondary) !important;
    line-height: 2.2rem !important;
    margin: 0px 2.4rem !important;
    padding-bottom: 0.8rem !important;
  }
}

@keyframes dialogFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
