<template>
  <div class="chart-wrapper" >
    <div :id="chartId" style="width: 100%;height: 15.5rem;"></div>
  </div>
</template>
<script>
import echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/markLine'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/grid'
export default {
  name: 'Chart',
  props: {
    reportType: {
      type: Number,
      default: 0 // 0: 日报, 1: 周报, 2: 月报
    },
    dataType: {
      type: String,
      default: 'co2' // 'co2', 'hcho', 'heat', 'moisture'
    },
    chartDataFromParent: {
      type: Array,
      default: null // 外部传入的图表数据
    }
  },
  data () {
    return {
      myChart: null,
      chartId: 'lineChart_' + Math.random().toString(36).substring(2, 11), // 生成唯一ID
      selectedDataIndex: -1 // 跟踪当前选中的数据点索引
    }
  },
  computed: {
    // 检测是否为暗黑模式
    isDarkMode() {
      const store = this.$store && this.$store.state && this.$store.state.isDarkMode;
      const appElement = document.getElementById('app');
      const domCheck = appElement && appElement.classList.contains('dark');
      return store || domCheck || false;
    },

    // 根据数据类型和报表类型返回不同的数据
    chartData() {
      // 优先使用外部传入的数据，但只有在有实际数据时才使用
      if (this.chartDataFromParent && Array.isArray(this.chartDataFromParent) && this.chartDataFromParent.length > 0) {
        return this.chartDataFromParent;
      }

      // 返回空数组作为默认值，避免undefined错误
      return [];
    },
    // 根据报表类型返回不同的X轴标签
    xAxisLabels() {
      switch (this.reportType) {
        case 0: // 日报 - 24小时时间轴，显示关键时间点
          return this.getDailyTimeLabels();
        case 1: // 周报 - 7天
          return this.getWeeklyLabels();
        case 2: // 月报 - 30天，选择性显示
          return this.getMonthlyLabels();
        case 3: // 年报 - 12个月
          return this.getYearlyLabels();
        default:
          return this.getDailyTimeLabels();
      }
    },


  },
  created () {
  },
  watch: {
    chartDataFromParent() {
      // 移除图表数据接收的详细日志
      // 当外部数据改变时重新渲染图表
      this.$nextTick(() => {
        if (this.myChart) {
          this.renderChart();
          // 数据变化后也选择最新数据点
          this.$nextTick(() => {
            setTimeout(() => {
              this.selectLatestDataPoint();
            }, 100);
          });
        }
      });
    },

    // 监听报表类型变化
    reportType() {
      this.$nextTick(() => {
        if (this.myChart) {
          this.renderChart();
          // 报表类型变化后也选择最新数据点
          this.$nextTick(() => {
            setTimeout(() => {
              this.selectLatestDataPoint();
            }, 100);
          });
        }
      });
    }
  },
  mounted () {
    this.renderChart()
    window.onresize = () => {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  },
  methods: {
    // 获取CSS变量值


    // 获取柱子宽度配置
    getBarWidth() {
      switch (this.reportType) {
        case 0: // 日报 - 较窄的柱子适应24小时显示
          return 8;
        case 1: // 周报 - 中等宽度
          return 12;
        case 2: // 月报 - 较窄适应30天显示
          return 6;
        case 3: // 年报 - 较宽适应12个月显示
          return 16;
        default:
          return 8;
      }
    },

    // 获取柱状图颜色
    getBarColor(opacity) {
      // 获取CSS变量中的高亮色
      const accentColor = getComputedStyle(document.documentElement).getPropertyValue('--emui_accent').trim();

      // 如果获取到CSS变量，使用它；否则使用默认颜色
      if (accentColor && accentColor !== '') {
        // 将十六进制颜色转换为rgba
        const hex = accentColor.replace('#', '');
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        return `rgba(${r}, ${g}, ${b}, ${opacity})`;
      }

      // 默认颜色（如果CSS变量获取失败）
      return `rgba(10, 89, 247, ${opacity})`;
    },

    // 获取文本颜色
    getTextColor(level = 'primary') {
      const colorMap = {
        'primary': '--emui_text_primary',
        'secondary': '--emui_text_secondary',
        'tertiary': '--emui_text_tertiary'
      };

      const cssVar = colorMap[level] || colorMap['primary'];
      const color = getComputedStyle(document.documentElement).getPropertyValue(cssVar).trim();

      // 如果获取到的是rgba格式，直接返回；如果是其他格式，使用默认值
      if (color && color !== '') {
        return color;
      }

      // 默认颜色（根据当前是否为暗黑模式）
      if (level === 'secondary') {
        return this.isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)';
      }
      return this.isDarkMode ? 'rgba(255, 255, 255, 0.86)' : 'rgba(0, 0, 0, 0.9)';
    },

    // 获取分割线颜色
    getDividerColor() {
      const color = getComputedStyle(document.documentElement).getPropertyValue('--emui_color_list_divider').trim();

      if (color && color !== '') {
        return color;
      }

      // 默认颜色（根据当前是否为暗黑模式）
      return this.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
    },

    // 获取系列配置（支持堆叠柱状图）
    getSeriesConfig() {
      // 检查数据格式，判断是否为区间数据
      const hasRangeData = this.chartData.some(item =>
        item && typeof item === 'object' && item.min !== undefined && item.max !== undefined
      );

      // 移除数据格式检查的详细日志

      if (hasRangeData) {
        // 区间数据：使用堆叠柱状图
        const minValues = this.chartData.map((item) => {
          if (!item) return 0;

          // 使用真正的最小值
          return Math.min(item.min, item.max);
        });
        const rangeValues = this.chartData.map((item) => {
          if (!item) return 0;

          // 确保max >= min，避免负值
          const minVal = Math.min(item.min, item.max);
          const maxVal = Math.max(item.min, item.max);
          return maxVal - minVal;
        });

        return [
          {
            name: 'Placeholder',
            type: 'bar',
            stack: 'Total',
            itemStyle: {
              borderColor: 'transparent',
              color: 'transparent'
            },
            emphasis: {
              itemStyle: {
                borderColor: 'transparent',
                color: 'transparent'
              }
            },
            data: minValues,
            barWidth: this.getBarWidth()
          },
          {
            name: 'Range',
            type: 'bar',
            stack: 'Total',
            itemStyle: {
              color: this.getBarColor(0.4),
              borderRadius: [6, 6, 6, 6],
              borderWidth: 0
            },
            select: {
              itemStyle: {
                color: this.getBarColor(1),
                borderRadius: [6, 6, 6, 6],
                borderWidth: 0
              }
            },
            emphasis: {
              itemStyle: {
                color: this.getBarColor(1),
                borderRadius: [6, 6, 6, 6],
                borderWidth: 0
              }
            },
            data: rangeValues,
            barWidth: this.getBarWidth(),
            markLine: {
              symbol: 'none',
              lineStyle: {
                color: this.getDividerColor(),
                width: 1,
                type: 'solid'
              },
              label: {
                show: false // 隐藏标签文字
              },
              data: [] // 初始为空，通过代码动态添加
            }
          }
        ];
      } else {
        // 单一数值：使用普通柱状图
        return [
          {
            data: this.chartData,
            type: 'bar',
            itemStyle: {
              color: this.getBarColor(0.4),
              borderRadius: [6, 6, 6, 6],
              borderWidth: 0
            },
            barWidth: this.getBarWidth(),
            select: {
              itemStyle: {
                color: this.getBarColor(1),
                borderRadius: [6, 6, 6, 6],
                borderWidth: 0
              }
            },
            emphasis: {
              itemStyle: {
                color: this.getBarColor(1),
                borderRadius: [6, 6, 6, 6],
                borderWidth: 0
              }
            }
          }
        ];
      }
    },

    // 选择最新的数据点
    selectLatestDataPoint() {
      if (!this.chartData || this.chartData.length === 0) return;

      // 找到最后一个有效数据点的索引
      let lastValidIndex = -1;
      for (let i = this.chartData.length - 1; i >= 0; i--) {
        if (this.chartData[i] !== null && this.chartData[i] !== undefined) {
          lastValidIndex = i;
          break;
        }
      }

      if (lastValidIndex >= 0 && this.myChart) {
        // 使用统一的选择逻辑
        this.selectDataPoint(lastValidIndex);
      }
    },

    // 生成日报时间标签（从昨天17:00到今天17:00，显示关键时间点）
    getDailyTimeLabels() {
      const labels = [];
      // 从17点开始，生成25小时的标签（昨天17:00到今天17:00）
      for (let i = 0; i < 25; i++) {
        const hour = (17 + i) % 24; // 从17点开始循环
        labels.push(String(hour).padStart(2, '0') + ':00');
      }
      return labels;
    },

    // 生成周报标签（7天，周一到周日）
    getWeeklyLabels() {
      const labels = [];
      // 获取本周一的日期
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0=周日, 1=周一, ..., 6=周六
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 计算到周一的偏移
      const monday = new Date(today);
      monday.setDate(today.getDate() + mondayOffset);

      // 生成周一到周日的标签
      for (let i = 0; i < 7; i++) {
        const date = new Date(monday);
        date.setDate(monday.getDate() + i);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        labels.push(`${month}/${day}`);
      }
      return labels;
    },

    // 生成月报标签（30天，使用数字序号）
    getMonthlyLabels() {
      const labels = [];
      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth();
      const daysInMonth = new Date(year, month + 1, 0).getDate();

      for (let i = 1; i <= daysInMonth; i++) {
        labels.push(String(i)); // 使用数字序号：1,2,3,4...30
      }
      return labels;
    },

    // 生成年报标签（12个月，使用数字序号）
    getYearlyLabels() {
      const labels = [];
      for (let i = 1; i <= 12; i++) {
        labels.push(String(i)); // 使用数字序号：1,2,3,4...12
      }
      return labels;
    },

    // 获取X轴标签显示间隔
    getXAxisLabelInterval() {
      switch (this.reportType) {
        case 0: // 日报 - 显示关键时间点：17:00, 23:00, 05:00, 11:00, 17:00
          // 使用函数来自定义显示哪些标签
          return (index) => {
            // 显示索引 0(17:00), 6(23:00), 12(05:00), 18(11:00), 24(17:00)
            return [0, 6, 12, 18, 24].includes(index);
          };
        case 1: // 周报 - 显示所有标签
          return 0;
        case 2: // 月报 - 每5天显示一个标签（1, 6, 11, 16, 21, 26, 31）
          return 4;
        case 3: // 年报 - 显示所有标签（1-12）
          return 0;
        default:
          return 5;
      }
    },

    renderChart () {
      // 销毁之前的图表实例（这样可以完全清除所有状态）
      if (this.myChart) {
        this.myChart.dispose();
      }

      this.myChart = echarts.init(document.getElementById(this.chartId))

      const option = {
        grid: {
          left: 16,
          top: 8,
          bottom: 8,
          right: 0,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            interval: this.getXAxisLabelInterval(),
            fontSize: 10,
            color: this.getTextColor('secondary'),
            margin: 6
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: this.getDividerColor()
            }
          },
          axisTick: {
            show: false, // 去掉x轴刻度线
            alignWithLabel: true, // 刻度线与标签对齐
            lineStyle: {
              color: this.getDividerColor()
            }
          },
          data: this.xAxisLabels
        },
        yAxis: {
          type: 'value',
          scale: false,
          position: 'right',
          boundaryGap: false,
          min: this.getYAxisMin(),
          max: this.getYAxisMax(),
          splitNumber: 4, // 固定显示4条刻度线
          interval: this.getYAxisInterval(), // 使用固定间隔
          axisLine: {
            show: false, // 隐藏y轴线条
            lineStyle: {
              color: this.getDividerColor()
            }
          },
          axisTick: {
            show: false, // 去掉y轴刻度线
            alignWithLabel: true, // 刻度线与标签对齐
            lineStyle: {
              color: this.getDividerColor()
            }
          },
          axisLabel: {
            show: true,
            fontSize: 10,
            color: this.getTextColor('secondary'),
            margin: 8,
            formatter: (value) => {
              // 根据数据类型使用不同的格式化规则
              if (this.dataType === 'hcho') {
                // 甲醛保留三位小数
                return parseFloat(value).toFixed(3);
              } else {
                // 其他数据类型（二氧化碳、温度、湿度）显示整数，无需小数
                return Math.round(parseFloat(value)).toString();
              }
            }
          },
          splitLine: {
            show: true, // 显示y轴平行于x轴的刻度线
            lineStyle: {
              color: this.getDividerColor(),
              width: 1,
              type: 'solid'
            }
          }
        },
        tooltip: {
          show: false // 完全禁用tooltip弹窗
        },
        series: this.getSeriesConfig()
      }

      this.myChart.setOption(option)

      // 添加点击事件处理
      let self = this
      self.myChart.on('click', function (params) {
        // 使用统一的选择逻辑
        self.selectDataPoint(params.dataIndex);
      })

      // 默认选择最新的数据点（最后一个）
      this.$nextTick(() => {
        setTimeout(() => {
          this.selectLatestDataPoint();
        }, 100);
      });


    },
    // 获取Y轴最小值
    getYAxisMin() {
      // 计算能容纳数据且显示4条刻度线的最小值
      if (this.chartData && this.chartData.length > 0) {
        let minValue = Infinity;
        let maxValue = -Infinity;

        // 处理不同数据格式，获取真实的最小值和最大值
        if (this.chartData[0] && typeof this.chartData[0] === 'object' && this.chartData[0].min !== undefined) {
          // 区间数据格式
          const validData = this.chartData.filter(item => item !== null);
          if (validData.length > 0) {
            minValue = Math.min(...validData.map(item => Math.min(item.min, item.max)));
            maxValue = Math.max(...validData.map(item => Math.max(item.min, item.max)));
          }
        } else {
          // 简单数组格式
          const validData = this.chartData.filter(item => item !== null && item !== undefined);
          if (validData.length > 0) {
            const values = validData.map(item => {
              if (typeof item === 'object' && item.min !== undefined) {
                return [Math.min(item.min || 0, item.max || 0), Math.max(item.min || 0, item.max || 0)];
              } else if (typeof item === 'number') {
                const val = isNaN(item) ? 0 : item;
                return [val, val];
              } else {
                return [0, 0];
              }
            });
            minValue = Math.min(...values.map(v => v[0]));
            maxValue = Math.max(...values.map(v => v[1]));
          }
        }

        // 根据固定刻度间隔计算合适的最小值
        if (minValue !== Infinity && maxValue !== -Infinity) {
          const interval = this.getYAxisInterval();
          if (interval) {
            // 计算数据的最小值，向下取整到间隔的倍数
            const dataMin = Math.floor(minValue / interval) * interval;
            const dataMax = Math.ceil(maxValue / interval) * interval;

            // 计算需要显示的范围，确保数据不会太挤
            const dataRange = dataMax - dataMin;

            // 如果数据范围太小，适当扩展范围
            if (dataRange <= interval) {
              // 数据范围很小时，向下扩展一个间隔
              return Math.max(0, dataMin - interval);
            } else {
              return dataMin;
            }
          }
        }
      }

      // 默认最小值为0
      return 0;
    },
    // 获取Y轴最大值
    getYAxisMax() {
      // 根据数据范围计算合适的最大值
      if (this.chartData && this.chartData.length > 0) {
        let maxValue = -Infinity;

        // 处理不同数据格式，获取真实的最大值
        if (this.chartData[0] && typeof this.chartData[0] === 'object' && this.chartData[0].max !== undefined) {
          // 区间数据格式
          const validData = this.chartData.filter(item => item !== null);
          if (validData.length > 0) {
            maxValue = Math.max(...validData.map(item => Math.max(item.min, item.max)));
          }
        } else {
          // 简单数组格式
          const validData = this.chartData.filter(item => item !== null);
          if (validData.length > 0) {
            const values = validData.map(item => {
              if (typeof item === 'object' && item.max !== undefined) {
                return Math.max(item.min || 0, item.max || 0);
              } else if (typeof item === 'number') {
                return isNaN(item) ? 0 : item;
              } else {
                return 0;
              }
            });
            maxValue = Math.max(...values);
          }
        }

        // 根据固定刻度间隔计算最大值
        if (maxValue !== -Infinity) {
          const interval = this.getYAxisInterval();
          if (interval) {
            const dataMax = Math.ceil(maxValue / interval) * interval;
            const minValue = this.getYAxisMin();
            const dataRange = dataMax - minValue;

            // 如果数据范围太小，适当扩展范围
            if (dataRange <= interval) {
              return dataMax + interval;
            } else {
              return dataMax;
            }
          }
        }
      }

      // 默认值（如果没有数据）
      switch (this.dataType) {
        case 'co2': return 2000;
        case 'hcho': return 0.04;
        case 'heat': return 30;
        case 'moisture': return 60;
        default: return 100;
      }
    },
    // 获取Y轴间隔
    getYAxisInterval() {
      // 返回固定的刻度间隔，确保Y轴刻度严格按照指定间隔显示
      switch (this.dataType) {
        case 'co2':
          return 2000; // 二氧化碳间隔2000
        case 'hcho':
          return 0.02; // 甲醛间隔0.02
        case 'heat':
          return 10; // 温度间隔10
        case 'moisture':
          return 20; // 湿度间隔20
        default:
          return null; // 其他情况让ECharts自动计算
      }
    },


    rightClick () {
      this.$emit('rightClick')
    },

    // 更新辅助线位置
    updateMarkLine(dataIndex) {
      if (!this.myChart || dataIndex < 0 || dataIndex >= this.xAxisLabels.length) {
        return;
      }

      // 获取当前图表配置
      const option = this.myChart.getOption();

      // 更新Range系列的markLine
      const seriesIndex = this.getSeriesConfig().length - 1; // Range系列索引
      if (option.series && option.series[seriesIndex]) {
        option.series[seriesIndex].markLine = {
          symbol: 'none',
          lineStyle: {
            color: this.getDividerColor(),
            width: 1,
            type: 'solid'
          },
          label: {
            show: false // 隐藏标签文字
          },
          data: [
            {
              xAxis: dataIndex,
              lineStyle: {
                color: this.getDividerColor(),
                width: 1,
                type: 'solid'
              },
              label: {
                show: false // 隐藏单个标记线的标签
              }
            }
          ]
        };

        // 更新图表配置
        this.myChart.setOption(option);
      }
    },

    // 选择数据点（统一的选择逻辑）
    selectDataPoint(dataIndex) {
      if (dataIndex < 0 || dataIndex >= this.chartData.length) {
        return;
      }

      // 更新选中索引
      this.selectedDataIndex = dataIndex;

      // 清除所有高亮
      this.myChart.dispatchAction({
        type: 'downplay'
      });

      // 高亮选中的数据点
      const targetSeriesIndex = this.getSeriesConfig().length - 1;
      this.myChart.dispatchAction({
        type: 'highlight',
        seriesIndex: targetSeriesIndex,
        dataIndex: dataIndex
      });

      // 更新辅助线
      this.updateMarkLine(dataIndex);

      // 获取数据值和标签
      const xAxisLabel = this.xAxisLabels[dataIndex];
      const dataValue = this.chartData[dataIndex];

      // 触发选择事件，通知父组件
      this.$emit('getValue', {
        name: xAxisLabel,
        value: dataValue
      });


    }
  },
}
</script>

<style lang="less">
.chart-wrapper {
  width: 100%;
  height: 15.5rem;

  &.disabled {
    opacity: .4;
  }

  .chart-title {
    margin: 1.6rem 0.8rem 3.2rem;
    height: 2.4rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      line-height: 2.4rem;
      height: 2.4rem;
      font-size: 1.4rem;
      color: var(--emui_text_primary);
    }

    .arrow {
      width: 1.2rem;
      height: 2.4rem;
      margin-left: 0.4rem;
      border: 0 none;
      background-size: cover;
      background-repeat: no-repeat;
      background-image: var(--img_ic_right_arrow);
    }
  }
}
</style>