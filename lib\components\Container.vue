<template>
  <div class="tw-container" :class="{'disabled' : disabled}">
    <div class="mod-hearder" v-if="title">
      <span class="title" :class="{bold : bold}">{{ title }}</span>
      <div :id="idStr != '' ? 'com.huawei.smarthome:id/container_' + idStr: ''" class="mod-right" v-if="rightText" @click.stop="rightClicked">
          <div class="ritem">
              <span>{{ rightText }}</span>
              <div class="arrow"></div>
          </div>
      </div>
    </div>
    <slot>

    </slot>
  </div>
</template>

<script>
export default {
  name: 'Container',
  props: {
    title: {
      default: null,
      type: String
    },
    rightText: {
      default: null,
      type: String
    },
    disabled: {
      default: true,
      type: Boolean
    },
    bold: {
       default: false,
       type: Boolean
    },
    idStr: {
      default: '',
      type: String
    }
  },
  methods: {
    rightClicked() {
      this.$emit('rightClick')
    }
  }
}
</script>

<style lang='less' scoped>
@import url("../style/public.less");

.tw-container {
  width: 100%;
  .cardStyle();
  box-sizing: border-box;
  position: relative;
  padding: 0px 0.8rem;

  &.disabled {
    opacity: .4;
  }

  .mod-hearder {
    margin: 0px 0.8rem;
    height: 4.8rem;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      margin-top: 1.4rem;
      line-height: 2.8rem;
      height: 2.8rem;
      font-size: 1.6rem;
      color: var(--emui_text_primary);
    }
    .bold{
       font-weight: bold;
    }

    .mod-right {
      display: flex;
      height: 4.8rem;
      align-items: center;
      font-size: 1.4rem;
      color: var(--emui_text_secondary);

      .ritem{
        margin-top: 1.4rem;
        height: 2.8rem;
        display: flex;
        align-items: center;

          .arrow {
              width: 1.2rem;
              height: 2.4rem;
              margin-left: 0.4rem;
              border: 0 none;
              background-size: cover;
              background-repeat: no-repeat;
              background-image: var(--img_ic_right_arrow);
          }
      }
    }
  }
}
</style>
