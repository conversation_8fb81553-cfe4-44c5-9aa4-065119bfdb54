// 字符串前后去空
export function Trim(str) {
    return str.replace(/(^\s*)|(\s*$)/g, '');
}
// try catch 函数集成精简
export function catchFn(fn, warnMsg, fn2) {
    let Msg;
    try {
        fn();
    } catch (err) {
        if (typeof fn2 === 'function') {
            fn2();
        } else if (typeof fn2 === 'object') {
            let timer = setTimeout(() => {
                window.deviceEventCallback(JSON.stringify(fn2));
                clearTimeout(timer);
            }, 500);
        }
        Msg = warnMsg || '未自定义的';
        console.hLog(new Time().logTime, `${Msg}错误信息: ${err}`);
    }
}
// 宽度检验
export function widthCheck(str, maxLen, key) {
    var w = 0;
    // length 获取字数数，不区分汉子和英文
    for (var i = 0; i < str.length; i++) {
        // charCodeAt()获取字符串中某一个字符的编码
        var c = str.charCodeAt(i);
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c => 0xff60 && c <= 0xff9f)) {
            w++;
        } else {
            w += 2;
        }
        if (w > maxLen) {
            this[key] = str.substr(0, i);
            break;
        }
    }
}
export function textValidateName(value) {
    // 设备名称检验
    var flag = '0';
    let pattern = /("|{|}|\/|\\)/; // 防止H5页面解析出问题
    if (value !== '' && value != null) {
        if (pattern.test(value)) {
            flag = '1';
        }
    }
    let regName1 = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\r\n]/g;
    let regName2 = /[^\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g;
    if (regName1.test(value) && regName2.test(value)) {
        flag = '1';
    }
    if (value.trim().length === 0) flag = '1'; // 不能输入空格符
    if (value === null || value === '') flag = '1';
    if (flag === '1') {
        return true;
    }
    return false;
}
export function widthLength(str) {
    var w = 0;
    // length 获取字数数，不区分汉子和英文
    for (var i = 0; i < str.length; i++) {
        // charCodeAt()获取字符串中某一个字符的编码
        var c = str.charCodeAt(i);
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
            w++;
        } else {
            w += 2;
        }
    }
    return w;
}
