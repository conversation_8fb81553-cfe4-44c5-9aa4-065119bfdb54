@import url("../../lib/style/public.less");

#product-wiki-page {
  overflow-y: auto;
  background-color: var(--emui_color_subbg);

  .content {
    // pad适配
    margin: 5.6rem 0.6rem 0px 0.6rem;
    position: relative;
    padding-bottom: 2.4rem;
  }

  // 产品图片展示区域
  .product-image-section {
    // margin-bottom: 2.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3.2rem 2.4rem;
    
    .product-image {
      max-width: 100%;
      max-height: 20rem;
      width: auto;
      height: auto;
      object-fit: contain;
      object-position: center;
    }
  }

  // 使用指南区域
  .guide-section {
    margin-bottom: 2.4rem;

    .section-title {
      font-size: 1.6rem;
      font-weight: 500;
      color: var(--emui_text_primary);
      margin-bottom: 1.6rem;
      padding-left: 1.6rem;
    }

    .guide-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 0.8rem;
      padding: 0 0.8rem;


      .guide-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        cursor: pointer;
        background-color: var(--emui_card_panel_bg);
        border-radius: 1.2rem;
        padding: 0.8rem 1.6rem;
        height: 5.6rem;
        .guide-icon {
          width: 2.4rem;
          height: 2.4rem;
          margin-bottom: 0.8rem;
          display: flex;
          align-items: center;
          justify-content: center;

          img {
            width: 2.4rem;
            height: 2.4rem;
            object-fit: contain;
          }
        }

        .guide-name {
          font-size: 0.9rem;
          color: var(--emui_text_primary);
          line-height: 1.4;
          word-break: break-all;
        }
      }
    }
  }

  // FAQ区域
  .faq-section {
    margin-bottom: 2.4rem;

    .section-title {
      font-size: 1.6rem;
      font-weight: 500;
      color: var(--emui_text_primary);
      margin-bottom: 1.2rem;
      padding-left: 1.6rem;
    }
  }
}
