# Week数据存储调试指南

## 问题描述

您反映"更改了week的数据没存进去"，这个问题可能出现在以下几个环节：

## 调试步骤

### 1. 检查数据类型一致性

我已经修复了以下问题：

#### ✅ profile.js 默认值修复
```javascript
// 修复前
"week": '0000000'  // 字符串格式

// 修复后  
"week": 0          // 数值格式
```

#### ✅ repeatOptions 值类型修复
```javascript
// 修复前
{ name: this.$t("run_once"), value: '0' }  // 字符串

// 修复后
{ name: this.$t("run_once"), value: 0 }    // 数值
```

### 2. 调试信息输出

现在所有关键方法都添加了详细的调试信息：

#### Timer Watcher
- 🔔 Timer watcher 触发
- 📋 Timer 原始数据
- 🔄 准备解析 week 值
- ✅ Timer数据解析完成

#### parseWeekToRepeatIndex
- 🔍 输入值和类型
- 📝 转换过程
- 🔢 转换后数值
- ⚙️ 设置前后的repeatIndex
- 🎯 自定义重复解析
- 🎉 解析完成总结

#### generateWeekString
- 🔄 输入的repeatIndex和customWeekDays
- 🎯 自定义重复的位运算过程
- 🎉 最终结果和二进制表示

### 3. 测试步骤

1. **打开睡眠页面**
   - 查看控制台是否有Timer watcher触发的日志
   - 确认初始数据加载是否正确

2. **修改重复设置**
   - 选择不同的重复选项（执行一次、工作日、周末、每日、自定义）
   - 观察控制台中selectRepeat的日志输出
   - 确认repeatIndex是否正确更新

3. **自定义重复测试**
   - 选择"自定义重复"
   - 在弹出的对话框中选择特定的星期几
   - 观察customWeekDays数组的变化

4. **保存设置**
   - 点击保存按钮
   - 观察generateWeekString的输出
   - 确认最终下发的week值是否正确

5. **数据持久化验证**
   - 保存后重新进入页面
   - 检查Timer watcher是否正确解析了存储的week值
   - 确认UI显示是否与存储的数据一致

### 4. 常见问题排查

#### 问题1: repeatIndex没有更新
**症状**: 选择重复选项后，UI没有变化
**排查**: 
- 检查selectRepeat方法的日志
- 确认value类型转换是否正确
- 验证repeatOptions的value是否为数值类型

#### 问题2: 自定义重复不生效
**症状**: 选择自定义重复后，week值计算错误
**排查**:
- 检查customWeekDays数组是否正确
- 验证位运算逻辑是否正确
- 确认generateWeekString的输出

#### 问题3: 数据下发失败
**症状**: 保存后数据没有持久化
**排查**:
- 检查setDevInfo的调用参数
- 验证commandData的格式是否正确
- 确认网络请求是否成功

#### 问题4: 数据解析错误
**症状**: 重新进入页面后，重复设置显示错误
**排查**:
- 检查Timer watcher的触发
- 验证parseWeekToRepeatIndex的输入和输出
- 确认数值和字符串格式的兼容性

### 5. 调试命令

在浏览器控制台中可以使用以下命令进行调试：

```javascript
// 查看当前Timer状态
console.log('Timer状态:', this.$store.state.timer);

// 查看当前repeatIndex
console.log('repeatIndex:', this.repeatIndex);

// 查看customWeekDays
console.log('customWeekDays:', this.customWeekDays);

// 手动测试week值生成
console.log('当前week值:', this.generateWeekString());

// 手动测试week值解析
this.parseWeekToRepeatIndex(127); // 测试每日重复
```

### 6. 预期的调试输出示例

#### 正常的工作日重复设置：
```
🔍 parseWeekToRepeatIndex 输入: 31 number
🔢 转换后的数值: 31
⚙️ 设置前 repeatIndex: 0
✅ 设置后 repeatIndex: 1
🎉 解析周重复完成: 输入值=31, 数值=31, 索引=1, 自定义天数=[]

🔄 generateWeekString 输入 repeatIndex: 1
🎉 generateWeekString 结果: 31 二进制: 0011111
```

#### 正常的自定义重复设置（周一、三、五）：
```
🔍 parseWeekToRepeatIndex 输入: 21 number
🔢 转换后的数值: 21
⚙️ 设置前 repeatIndex: 1
✅ 设置后 repeatIndex: 4
🎯 自定义重复解析: [0,2,4]
🎉 解析周重复完成: 输入值=21, 数值=21, 索引=4, 自定义天数=[0,2,4]

🔄 generateWeekString 输入 repeatIndex: 4
🔄 customWeekDays: [0,2,4]
🎯 设置第0天, 当前值: 1
🎯 设置第2天, 当前值: 5
🎯 设置第4天, 当前值: 21
🎉 generateWeekString 结果: 21 二进制: 0010101
```

## 解决方案总结

1. ✅ 修复了profile.js中week的默认值类型
2. ✅ 修复了repeatOptions的value类型
3. ✅ 添加了完整的调试日志
4. ✅ 优化了数据下发格式
5. ✅ 增强了类型转换的兼容性

现在请按照上述步骤进行测试，并查看控制台输出来定位具体的问题所在。
