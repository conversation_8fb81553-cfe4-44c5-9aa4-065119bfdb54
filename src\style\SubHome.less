@import url("../../lib/style/public.less");

#subhome-page {
    overflow-y: auto;

    .content {
        // pad适配
        margin: 5.6rem var(--home_margin) 0px var(--home_margin);
        position: relative;
    }

    // 环境质量概览区域
    .environment-section {
        margin-bottom: 2.4rem;
    }

    // 自定义Tab栏样式
    .tab-wrapper {
        width: calc(100% - 3.6rem);
        margin: 0 auto;
        height: 5.6rem;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .tab-item {
            margin-left: 11rem;
            height: 3rem;
            margin-top: 1.6rem;
            margin-bottom: 0.8rem;
            display: flex;
            flex-direction: column;
            font-size: 1.6rem;
            line-height: 2.2rem;
            color: var(--emui_text_secondary);
        }

        .tab-item:nth-child(2) {
            margin-left: 0;
            margin-right: 11rem;
        }

        .active {
            color: var(--emui_accent);

            .line {
                width: 100%;
                height: 0.2rem;
                margin-top: 0.6rem;
                background-color: var(--emui_accent);
                border-radius: 0.1rem;
            }
        }
    }
    .tab-content {
        width: 100%;
    }

    // 日期选择器样式
    .date-selector {
        width: calc(100% - 3.6rem);
        margin: 0.8rem auto 0.8rem auto;
        display: flex;
        align-items: center;
        justify-content: space-between;

        // 左右导航按钮
        .date-nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.2rem;
            height: 2.4rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 50%;

            &:hover {
                background-color: var(--emui_color_bg_secondary);
            }

            &:active {
                background-color: var(--emui_color_bg_tertiary);
                transform: scale(0.95);
            }

            svg {
                transition: all 0.3s ease;
            }
        }

        // 中间日期显示区域
        .date-display {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1.2rem 1.6rem;
            margin: 0 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 0.8rem;

            &:hover {
                background-color: var(--emui_color_bg_secondary);
            }

            &:active {
                background-color: var(--emui_color_bg_tertiary);
                transform: scale(0.98);
            }

            .date-text {
                font-size: 1.6rem;
                color: var(--emui_text_primary);
                font-weight: 500;
                margin-right: 0.8rem;
                white-space: nowrap;
            }

            .date-arrow {
                display: flex;
                align-items: center;
                transition: transform 0.3s ease;

                &:hover {
                    transform: translateY(1px);
                }
            }
        }
    }

    .module-box {
        margin: 0 .6rem;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        align-items: flex-start;
        justify-content: flex-start;

        .container {
            padding: 0.4rem 0;

            .switch-bar {
                height: 4.8rem;

                /deep/ .control-bar {
                    padding: 0 1.2rem;
                    height: 4.8rem;
                }
            }
        }
    }

    .environment-section {
        background-color: var(--emui_color_bg);
        .cardStyle();
        padding: 0px 0.8rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-bottom: 2.4rem;
    }
}

// 暗黑模式适配
.dark #page {
    .custom-tabs {
        .tab-header {
            border-bottom-color: var(--emui_color_divider);
        }
    }
}
