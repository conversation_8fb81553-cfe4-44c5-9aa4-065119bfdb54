<template>
  <div class="list-item" @click="handleClick">
    <div class="left">
      <span class="name">{{ name }}</span>
    </div>
    <div class="right">
      <img v-if="iconName" class="icon" :src="require(`../../../src/assets/${iconName}`)" :alt="name" />
      <div v-else class="arrow"></div>
    </div>
    <div v-if="divider" class="list-item-divider"></div>
  </div>
</template>

<script>
export default {
  name: 'ListItemNav',
  props: {
    name: {
      default: '',
      type: String
    },
    iconName: {
      default: '',
      type: String
    },
    divider: {
      default: true,
      type: Boolean
    }
  },
  methods: {
    handleClick () {
      this.$emit('click');
    }
  }
}
</script>

<style lang='less' scoped>
@import url("../../style/public.less");

.list-item {
  margin: 0px 0.6rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 4.8rem;
  cursor: pointer;

  .left {
    flex: 1;
    margin: 0.8rem 0;

    .name {
      font-size: 1.4rem;
      color: var(--emui_text_primary);
    }
  }

  .right {
    display: flex;
    align-items: center;
    margin: 0.8rem 0;

    .icon {
      width: 2.4rem;
      height: 2.4rem;
      object-fit: contain;
    }

    .arrow {
      flex-shrink: 0;
      width: 1.2rem;
      height: 2.4rem;
      border: 0 none;
      background-size: cover;
      background-repeat: no-repeat;
      background-image: var(--img_ic_right_arrow);
    }
  }

  .list-item-divider {
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: 0;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }
}
</style>
