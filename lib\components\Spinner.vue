<template>
  <div>
    <div class="dialog" v-if="show" @click="cancel" style="background: transparent"></div>
    <ul class="spinnerWrap" :class="{'spinnerAppear': show}" :style="{height: (show ? spinnerHeight : '0px')}">
      <div class="spinnerList" :style="{height: listHieht}">
        <li class="spinnerItem" v-for="(item, index) in values" :id="'spinnerItem' + index" :key="index" @click.stop="confirm(item)"
            :class="{'checkedBg': item.value == value}">{{ item.name }}
        </li>
      </div>
    </ul>
  </div>
</template>
<script>

export default {
  props: {
    show: {
      default: false,
      type: Boolean
    },
    value: {
      default: 0,
      type: Number
    },
    values: {
      default: Array(),
      type: Array
    },
  },
  name: 'Spinner',
  methods: {
    cancel() {
      this.$emit('cancel');
    },
    confirm(item) {
      this.$emit('confirm', item);
    }
  },
  computed: {
    spinnerHeight() {
      return (((this.values.length > 5 ? 5 : this.values.length) * 4.8) + 0.8) + 'rem';
    },
    listHieht() {
      return ((this.values.length > 5 ? 5 : this.values.length) * 4.8) + 'rem';
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.spinnerWrap {
  position: absolute;
  width: calc(100% - 1.2rem);
  height: 0px;
  overflow: hidden;
  background: var(--emui_dropbox_bg);
  border-radius: 1.6rem;
  bottom: 8.8rem;
  left: 0.6rem;
  box-shadow: 0px 1rem 5rem 1px var(--emui_dropbox_bg_shadow);
  display: block;
  padding: 0px;
  z-index: 10000;
  transition: height .3s;

  .spinnerList {
    margin: 0.4rem;
    overflow-x: hidden;
    overflow-y: auto;

    .spinnerItem {
      font-size: 1.6rem;
      height: 4.8rem;
      line-height: 4.8rem;
      color: var(--emui_text_primary);
      position: relative;
      padding-left: 1.2rem;
      box-sizing: border-box;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      &:after {
        position: absolute;
        width: calc(100% - 2.4rem);
        content: '';
        display: block;
        height: 1px;
        bottom: 0;
        transform: scaleY(0.25);
        background: var(--emui_color_divider_horizontal);
        z-index: 101;
      }

      &:last-child:after {
        display: none;
      }
    }

    .checkedBg {
      border-radius: 1.2rem;
      background: var(--option_select_bg);
      color: var(--emui_accent);
    }

    &::-webkit-scrollbar {
      width: 0.4rem;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 0.4rem;
      background-color: var(--emui_text_tertiary);
      transform: translateX(-1rem);
    }

    &::-webkit-scrollbar-thumb:window-inactive {
      background-color: var(--emui_text_tertiary);
      transform: translateX(-1rem);
    }
  }

}

.spinnerAppear {
  display: inline-block;
  -webkit-transition: height .3s;
  transition: height .3s;
}
</style>
