<template>
  <div id="wiki-usage-tips-page">
    <Titlebar
      :title="$t('wiki_usage_tips')"
      :showRightIcon="false"
      @leftClick="$router.goBack()"
    />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      
      <div class="wiki-content">
        <div class="section">
          <div class="section-content">
            <div class="item-text">请避免以下操作，以免影响设备探测的准确性：</div>
            
            <div class="tips-list">
              <div class="tip-item">
                <div class="tip-number">1、</div>
                <div class="tip-text">使用酒精及其他化学物品清洁设备。</div>
              </div>
              
              <div class="tip-item">
                <div class="tip-number">2、</div>
                <div class="tip-text">长时间将设备至于高浓度污染、高湿度环境中。</div>
              </div>
              
              <div class="tip-item">
                <div class="tip-number">3、</div>
                <div class="tip-text">靠近热源过大、其它设备出风口等位置。</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { goBack } from '../util/mixins';

export default {
  name: 'WikiUsageTips',
  mixins: [goBack],
  data() {
    return {
      dialogList: [] // 没有弹窗，空数组
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight'])
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
  }
};
</script>

<style lang="less" scoped>
@import url("../../lib/style/public.less");

#wiki-usage-tips-page {
  overflow-y: auto;
  background-color: var(--emui_color_subbg);

  .content {
    margin: 6.6rem 2.4rem 0px 2.4rem;
    position: relative;

    .wiki-content {

      .section {
        margin-bottom: 2rem;

        .section-title {
          font-size: 1.8rem;
          font-weight: 600;
          color: var(--emui_text_primary);
          margin-bottom: 1.6rem;
          line-height: 1.4;
        }
        
        .section-content {
          .item-text {
            font-size: 1.4rem;
            color: var(--emui_text_secondary);
            line-height: 1.6;
            margin-bottom: 1.6rem;
          }
          
          .tips-list {
            .tip-item {
              display: flex;
              align-items: flex-start;
              margin-bottom: 1.2rem;
              
              .tip-number {
                font-size: 1.4rem;
                color: var(--emui_text_primary);
                font-weight: 500;
                min-width: 2rem;
                line-height: 1.6;
              }
              
              .tip-text {
                font-size: 1.4rem;
                color: var(--emui_text_secondary);
                line-height: 1.6;
                flex: 1;
              }
            }
          }
        }
      }
    }
  }
}

// 决策理由：适配平板设备的边距
.pad #wiki-usage-tips-page {
  .content {
    margin: 6.6rem var(--home_margin) 0px var(--home_margin);
  }
}
</style>
