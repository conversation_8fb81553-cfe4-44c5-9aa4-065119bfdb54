# MessageBox

#### 消息弹窗  例如蓝牙链接失败组件

| Event | 描述 |
| --- | --- |
| title    | 提示框的标题	|
| message   |提示框的内容	|
| showConfirmButton   |是否显示确认按钮		默认true|
| showCancelButton   |是否显示取消按钮		默认true|
| confirmButtonText   |确认按钮的文本		|
| cancelButtonText   |取消按钮的文本		|
| confirmButtonClass   |确认按钮的类名		默认不写|
| cancelButtonClass   |取消按钮的类名		默认不写	|

#### ![](./images/connect_fail.png)

```` JavaScript
 this.$messagebox.show({
    title: this.$t('public_connect_fail'),
    message: this.$t('public_connect_fail_msg'),
    cancelButtonText: this.$t('public_try_again_later'),
    confirmButtonText: this.$t('reconnect')
  }).then(() => this.$refs.statusBar.reconnect())
````
