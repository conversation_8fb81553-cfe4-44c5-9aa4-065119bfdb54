<template>
  <div id="wiki-troubleshooting-page">
    <Titlebar
      :title="$t('wiki_troubleshooting')"
      :showRightIcon="false"
      @leftClick="$router.goBack()"
    />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      
      <div class="wiki-content">
        <div class="section">
          <div class="subsection">
            <div class="subsection-title">1）网络连接不上</div>
            <div class="subsection-content">
              <div class="item-text">网络连接失败，可能的原因：</div>
              
              <div class="item-text"><strong>Wi-Fi为非2.4GHz频段</strong></div>
              <div class="item-text">解决办法：设备仅支持2.4GHz Wi-Fi。请进入路由器控制页面，打开2.4Ghz Wi-Fi，并在设备配网过程中，选择该2.4GHz Wi-Fi及输入密码，重新尝试连接。</div>

              <div class="item-text"><strong>Wi-Fi信号弱通讯不稳定</strong></div>
              <div class="item-text">解决办法：设备与Wi-Fi之间的障碍物（如电冰箱、电视机、墙面）会导致信号衰减，请尝试将设备靠近路由器，重新尝试连接。</div>

              <div class="item-text"><strong>W-iFi已更改密码</strong></div>
              <div class="item-text">解决办法：重置设备网络（长按设备按键≥6S），输入新密码，重新连接网络。</div>
            </div>
          </div>

          <div class="subsection">
            <div class="subsection-title">2）故障码含义</div>
            <div class="subsection-content">
              <div class="item-text">如设备出现故障，将会在设备屏幕上显示故障码"----"，对应含义为该传感器故障。如二氧化碳传感器实时数据区域显示"----"，表示二氧化碳传感器故障。</div>
              <div class="item-text">如设备提示故障码，请联系售后服务处理。</div>
            </div>
          </div>

          <div class="subsection">
            <div class="subsection-title">3）寿命到期提醒</div>
            <div class="subsection-content">
              <div class="item-text">APP收到消息提醒：</div>
              
              <div class="item-text"><strong>提醒方案一：</strong></div>
              <div class="item-text">您使用四合一空气质量传感器已超过3年，由于甲醛传感器的电化学特性，检测精度可能已经产生了偏移，建议您更换设备。</div>

              <div class="item-text"><strong>提醒方案二：</strong></div>
              <div class="item-text">您的四合一空气质量传感器已持续守护家庭健康超过3年，由于甲醛传感器采用电化学原理，长期使用后检测精度可能逐渐偏移。为保障数据始终可靠，建议您更换设备。</div>

              <div class="item-text"><strong>提醒方案三：</strong></div>
              <div class="item-text">您的四合一空气质量传感器已忠诚服役超过3年，感谢这份长久的信任！甲醛传感器采用电化学原理，其内部反应材料为消耗品（类似电池），当前传感器反应材料完全耗尽，为保障您的健康决策可靠性，我们郑重建议您：更换整机设备！ （查看更多资讯 www.xxx.com）</div>

              <div class="item-text"><strong>是什么原因？</strong></div>
              <div class="item-text">是由于甲醛传感器的寿命已到期。</div>
              <div class="item-text">根据甲醛传感器的电化学原理，传感器内置的化学材料会和空气当中的甲醛气体发生化学反应，从而消耗该化学材料。消耗速度会根据使用环境中的甲醛浓度而变化，甲醛浓度越高，该材料消耗更快；同时，高湿环境下也会缩短甲醛传感器（内置化学反应材料）的寿命。</div>
              <div class="item-text">如收到以上提醒，建议您更换设备。</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { goBack } from '../util/mixins';

export default {
  name: 'WikiTroubleshooting',
  mixins: [goBack],
  data() {
    return {
      dialogList: [] // 没有弹窗，空数组
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight'])
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
  }
};
</script>

<style lang="less" scoped>
@import url("../../lib/style/public.less");

#wiki-troubleshooting-page {
  overflow-y: auto;
  background-color: var(--emui_color_subbg);

  .content {
    margin: 6.6rem 2.4rem 0px 2.4rem;
    position: relative;

    .wiki-content {

      .section {
        margin-bottom: 2rem;

        .section-title {
          font-size: 1.8rem;
          font-weight: 600;
          color: var(--emui_text_primary);
          margin-bottom: 1.6rem;
          line-height: 1.4;
        }
        
        .subsection {
          margin-bottom: 3rem;
          
          .subsection-title {
            font-size: 1.6rem;
            font-weight: 500;
            color: var(--emui_text_primary);
            margin-bottom: 1.6rem;
            line-height: 1.4;
          }
          
          .subsection-content {
            .item-text {
              font-size: 1.4rem;
              color: var(--emui_text_secondary);
              line-height: 1.6;
              margin-bottom: 1.2rem;
            }
            

          }
        }
      }
    }
  }
}

// 决策理由：适配平板设备的边距
.pad #wiki-troubleshooting-page {
  .content {
    margin: 6.6rem var(--home_margin) 0px var(--home_margin);
  }
}
</style>
