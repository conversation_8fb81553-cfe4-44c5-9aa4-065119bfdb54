<!--
 * @Author: y<PERSON><PERSON><PERSON><PERSON>@talkweb.com.cn
 * @Date: 2025-06-16 10:44:37
 * @LastEditTime: 2025-08-04 17:40:34
 * @LastEditors: Numb丶 <EMAIL>
-->
<template>
    <div id="page">
        <Titlebar :title="devName" @leftClick="closeWindow()" @rightClick="jumpTo" />
        <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
            <Alert ref="alertMessage"></Alert>
            <DeviceShow ref="deviceShow" :product="require('@/assets/product.png')"
                :logo="require('@/assets/logo.png')" />
            <div class="module-box">
                <BleStatusBar ref="statusBar" :connectStatus="connectStatus">
                    <div class="status-right">
                        <img :src="ElcImage" class="electric-img" alt />
                        <span>{{ BatteryLevel }}</span>
                        <span>%</span>
                    </div>
                </BleStatusBar>
                <EnvironmentOverview :co2Current="Co2Current" :co2Level="Co2Level" :hchoCurrent="HchoCurrent"
                    :temperature="HeatCurrentFloat" :humidity="MoistureCurrent" :disabled="!canControl"
                    @click="jumpToEnvironmentDetail" />
                <InfoBar :disabled="!canControl">
                    <div class="block-info info-item">
                        <div v-if="canControl">
                            {{ Co2Current
                            }}<span>{{ $t("Co2Current_unit") }}</span>
                        </div>
                        <div v-else>- -</div>
                        <span style="
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                            " @click="handleToPage(2)">{{ $t("Co2Current_sub")
                            }}<img style="
                                    width: 0.8rem;
                                    height: 1.6rem;
                                    margin-left: 0.6rem;
                                " :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''
                                    }ic_right.png`)
                                    " /></span>
                    </div>
                    <div class="block-info info-item">
                        <div :class="Co2LevelClassObject" v-if="canControl">
                            {{ Co2LevelInfoText }}
                        </div>
                        <div v-else>- -</div>
                    </div>
                </InfoBar>
                <InfoBar :disabled="!canControl">
                    <div class="block-info info-item">
                        <div v-if="canControl">
                            {{ HchoCurrent
                            }}<span>{{ $t("HchoCurrent_unit") }}</span>
                        </div>
                        <div v-else>- -</div>
                        <span style="
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                            " @click="handleToPage(3)">{{ $t("HchoCurrent_sub")
                            }}<img style="
                                    width: 0.8rem;
                                    height: 1.6rem;
                                    margin-left: 0.6rem;
                                " :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''
                                    }ic_right.png`)
                                    " /></span>
                    </div>
                    <div class="block-info info-item">
                        <div :class="HchoLevelClassObject" v-if="canControl">
                            {{ HchoLevelInfoText }}
                        </div>
                        <div v-else>- -</div>
                    </div>
                </InfoBar>
                <InfoBar :disabled="!canControl">
                    <div class="block-info info-item">
                        <div v-if="canControl">
                            {{ HeatCurrentFloat
                            }}<span>{{ $t("HeatCurrentFloat_unit") }}</span>
                        </div>
                        <div v-else>- -</div>
                        <span style="
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                            " @click="handleToPage(4)">{{ $t("HeatCurrentFloat_sub")
                            }}<img style="
                                    width: 0.8rem;
                                    height: 1.6rem;
                                    margin-left: 0.6rem;
                                " :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''
                                    }ic_right.png`)
                                    " /></span>
                    </div>
                    <div class="block-info info-item">
                        <div v-if="canControl">
                            {{ MoistureCurrent
                            }}<span>{{ $t("MoistureCurrent_unit") }}</span>
                        </div>
                        <div v-else>- -</div>
                        <span style="
                                display: flex;
                                flex-direction: row;
                                align-items: center;
                            " @click="handleToPage(5)">{{ $t("MoistureCurrent_sub")
                            }}<img style="
                                    width: 0.8rem;
                                    height: 1.6rem;
                                    margin-left: 0.6rem;
                                " :src="require(`../assets/${$store.state.isDarkMode ? 'dark/' : ''
                                    }ic_right.png`)
                                    " /></span>
                    </div>
                </InfoBar>
                <ControlBar ref="screenOnControlBar" idStr="ScreenOn" :name="$t('ScreenOn')" :info="ScreenOnDisplayText"
                    iconName="ic_ScreenOn" @handleClick="showScreenOnPicker"
                    :disabled="!canControl || connectStatus == 0 || BatteryCharging == 2"
                    :active="ScreenModeDelayTime != 0 && canControl" :tip="$t('ScreenOn_tip')"></ControlBar>
                <ControlBar ref="timerControlBar" idStr="Timer" :name="$t('Timer')" iconName="ic_Timer"
                    @handleClick="handleToSleepPage" :disabled="!canControl || connectStatus == 0"
                    :tip="$t('Timer_tip')" :showTipRight="false"
                    :active="Timer.length != 0 && Timer && Timer[0].enable == 1 && canControl"></ControlBar>
                <SubTitle :title="$t('product_title')"></SubTitle>
                <ProductCard :mainText="$t('product_wiki')" :subText="$t('product_description')"
                    @click="handleProductCardClick" />
                <SubTitle :title="$t('saleservice_title')"></SubTitle>
                <ServiceCard :mainText="$t('after_sales_service')" :subText="$t('service_description')"
                    @click="handleServiceCardClick" />
            </div>
        </div>
        <DialogRadioPicker v-if="isShowScreenOnPicker" :key="ScreenOnPickerIndex" :values="ScreenOnItems"
            :title="$t('dialog_title_ScreenOn')" :subtitle="$t('dialog_subtitle_ScreenOn')"
            :defaultIndex="ScreenOnPickerIndex" @confirm="onScreenOnConfirm" @cancel="onScreenOnCancel">
        </DialogRadioPicker>

        <!-- 电池低电量警告弹窗 -->
        <BatteryAlarmDialog :visible="showBatteryAlarmDialog" @confirm="handleBatteryAlarmConfirm" />
    </div>
</template>
<script>
import { mapState, mapGetters, mapMutations, mapActions } from "vuex";
import EnvironmentOverview from "./EnvironmentOverview.vue";
import ServiceCard from "./ServiceCard.vue";
import ProductCard from "./ProductCard.vue";
import BatteryAlarmDialog from "../components/BatteryAlarmDialog.vue";
export default {
    name: "home",
    components: {
        EnvironmentOverview,
        ServiceCard,
        ProductCard,
        BatteryAlarmDialog,
    },
    data () {
        return {
            Co2LevelItems: [
                { name: this.$t("item_Co2Level_1"), value: 1 },
                { name: this.$t("item_Co2Level_2"), value: 2 },
                { name: this.$t("item_Co2Level_3"), value: 3 },
            ],
            HchoLevelItems: [
                { name: this.$t("item_HchoLevel_1"), value: 1 },
                { name: this.$t("item_HchoLevel_2"), value: 2 },
                { name: this.$t("item_HchoLevel_3"), value: 3 },
            ],
            isShowScreenOnPicker: false,
            ScreenOnItems: [
                { name: this.$t("screen_time_disabled"), value: 0 },
                { name: this.$t("screen_time_30s"), value: 1 },
                { name: this.$t("screen_time_1m"), value: 2 },
                { name: this.$t("screen_time_2m"), value: 3 },
                { name: this.$t("screen_time_3m"), value: 4 },
                { name: this.$t("screen_time_4m"), value: 5 },
                { name: this.$t("screen_time_5m"), value: 6 },
            ],
            showBatteryAlarmDialog: false,
            // 首次进入tip显示控制
            isFirstVisit: false,
        };
    },
    computed: {
        ...mapGetters(["headerHeight", "statusBarHeight", "canControl"]),
        ...mapState({
            devName: (state) => state.devName,
            Co2Current: (state) => state.co2.current,
            Co2Level: (state) => state.co2.level,
            HchoCurrent: (state) => state.hcho.currentFloat,
            HchoLevel: (state) => state.hcho.level,
            HeatCurrentFloat: (state) => state.heat.currentFloat,
            MoistureCurrent: (state) => state.moisture.current,
            ScreenModeDelayTime: (state) => state.screenMode.delayTime,
            Timer: (state) => state.timer.timer,
            BatteryLevel: (state) => state.battery.level,
            BatteryAlarm: (state) => state.battery.alarm,
            BatteryCharging: (state) => state.battery.charging,
            CommonFaultDetectionCode: (state) => state.commonFaultDetection.code,
            deviceStatus: (state) => state.status,
        }),
        ElcImage () {
            if (this.BatteryCharging == 1) {
                return require(`../assets/electric_on.png`);
            }
            if (0 <= this.BatteryLevel && this.BatteryLevel < 10) {
                return require(`../assets/${this.$store.state.isDarkMode ? "dark/" : ""
                    }electric_1.png`);
            } else if (this.BatteryLevel >= 10 && this.BatteryLevel < 30) {
                return require(`../assets/${this.$store.state.isDarkMode ? "dark/" : ""}electric_2.png`);
            } else if (this.BatteryLevel >= 30 && this.BatteryLevel < 75) {
                return require(`../assets/${this.$store.state.isDarkMode ? "dark/" : ""}electric_3.png`);
            } else if (this.BatteryLevel >= 75 && this.BatteryLevel <= 100) {
                return require(`../assets/${this.$store.state.isDarkMode ? "dark/" : ""}electric_4.png`);
            }
        },
        Co2LevelClassObject () {
            return {
                "color-64BB5C": 0 < this.Co2Level && this.Co2Level <= 1,
                "color-F7CE00": 1 < this.Co2Level && this.Co2Level <= 2,
                "color-E84026": 2 < this.Co2Level && this.Co2Level <= 3,
            };
        },
        Co2LevelInfoText () {
            const index = this.Co2LevelItems.findIndex(
                (item) => item.value == this.Co2Level
            );
            return index >= 0 && index < this.Co2LevelItems.length
                ? this.Co2LevelItems[index].name
                : "";
        },
        HchoLevelClassObject () {
            return {
                "color-64BB5C": 0 < this.HchoLevel && this.HchoLevel <= 1,
                "color-F7CE00": 1 < this.HchoLevel && this.HchoLevel <= 2,
                "color-E84026": 2 < this.HchoLevel && this.HchoLevel <= 3,
            };
        },
        HchoLevelInfoText () {
            const index = this.HchoLevelItems.findIndex(
                (item) => item.value == this.HchoLevel
            );
            return index >= 0 && index < this.HchoLevelItems.length
                ? this.HchoLevelItems[index].name
                : "";
        },
        ScreenOnPickerIndex () {
            console.log("ScreenModeDelayTime:" + this.ScreenModeDelayTime)
            // 根据ScreenModeDelayTime的值找到对应的数组索引
            const index = this.ScreenOnItems.findIndex(item => item.value === this.ScreenModeDelayTime);
            console.log("找到的索引:", index);
            return index >= 0 ? index : 0; // 如果找不到，默认返回0
        },
        ScreenOnDisplayText () {
            if (this.ScreenModeDelayTime === 0) {
                return null; // 不启用时不显示文本
            }
            const item = this.ScreenOnItems.find(
                (item) => item.value === this.ScreenModeDelayTime
            );
            return item ? item.name : null;
        },
        // 决策理由：根据设备状态动态计算BleStatusBar的connectStatus值
        connectStatus () {
            if (this.deviceStatus === 'offline' || this.deviceStatus === 'disconnected') {
                return 0; // 显示"重新连接"
            } else if (this.deviceStatus === 'connecting') {
                return 1; // 显示loading动画
            } else if (this.deviceStatus === 'online' || this.deviceStatus === 'connected') {
                return 2; // 显示已连接状态
            } else {
                // 默认情况，如果状态未知或为空，显示已连接
                return 2;
            }
        },
    },
    activated () {
        console.log("2222")
        window.goBack = () => {
            if (this.isShowScreenOnPicker) {
                this.isShowScreenOnPicker = false;
                return
            }
            if (this.showBatteryAlarmDialog) {
                this.showBatteryAlarmDialog = false;
                return
            }
            this.$store.dispatch("finishDeviceActivity");
        }
    },
    created () {
        this.$store.dispatch("getDevInfoAll");

        this.$store.dispatch("setTitleVisible", false);

        // 决策理由：页面加载时检查当前电池状态，如果已经是警告状态则显示弹窗
        this.checkBatteryAlarm();

        // 决策理由：检查是否首次访问，如果是则显示tip提示
        this.checkFirstVisitTips();
    },
    methods: {
        // 首页
        closeWindow () {
            this.$store.dispatch('finishDeviceActivity');
        },
        goBack () { //返回上一页
            this.$router.goBack()
        },
        jumpTo () {
            this.$store.dispatch("jumpToDeviceSetting");
        },
        jumpToEnvironmentDetail () {
            console.log("跳转到环境详情页面");
            // 可以跳转到环境详情页面或显示详细信息
            this.$router.push({ path: "./SubHome", query: { type: 1 } });
        },
        handleToPage (type) {
            console.log(type)
            this.$router.push({ path: "./SubHome", query: { type: type } });

        },
        sendCommond (sid, chara, value, curValue) {
            if (!this.canControl || value === curValue) {
                return;
            }
            const data = {
                [sid]: {
                    [chara]: value,
                },
            };
            this.$store.dispatch("setDevInfo", data);
        },
        formatTime (time, format, unit) {
            if (unit == 0) {
                return this.$moment.duration(time, "seconds").format(format);
            } else if (unit == 1) {
                return this.$moment.duration(time, "minutes").format(format);
            } else if (unit == 2) {
                return this.$moment.duration(time, "hours").format(format);
            }
            return time;
        },
        showScreenOnPicker () {
            if (!this.canControl) {
                return;
            }
            this.isShowScreenOnPicker = true;
        },
        onScreenOnConfirm (selectedValue) {
            console.log('智能熄屏参数下发:', selectedValue);
            this.isShowScreenOnPicker = false;
            const actualValue =
                typeof selectedValue === "object" &&
                    selectedValue.value !== undefined
                    ? selectedValue.value
                    : selectedValue;
            this.sendCommond('screenMode', 'delayTime', actualValue, this.ScreenModeDelayTime);
        },
        onScreenOnCancel () {
            this.isShowScreenOnPicker = false;
        },

        // 根据delay枚举值计算具体的时间值（秒）
        getDelayTimeValue (delayEnum) {
            const timeMap = {
                0: 0,      // 不启用
                1: 30,     // 30秒
                2: 60,     // 1分钟
                3: 120,    // 2分钟
                4: 180,    // 3分钟
                5: 240,    // 4分钟
                6: 300     // 5分钟
            };
            return timeMap[delayEnum] || 0;
        },
        handleToSleepPage () {
            this.$router.push({ path: "./SleepPage" });
        },
        handleServiceCardClick () {
            console.log("售后服务卡片被点击");
            // 跳转到售后服务页面
            this.$router.push('/AfterSalesService');
        },
        handleProductCardClick () {
            console.log("产品百科卡片被点击");
            // 跳转到产品百科页面
            this.$router.push('/ProductWiki');
        },
        handleBatteryAlarmConfirm (dontRemindAgain) {
            // 决策理由：用户确认后关闭弹窗，如果选择了"不再提醒"则保存到localStorage
            this.showBatteryAlarmDialog = false;
            if (dontRemindAgain) {
                localStorage.setItem('batteryAlarmDontRemind', 'true');
            }
        },
        checkBatteryAlarm () {
            console.log(localStorage.getItem('batteryAlarmDontRemind') === 'true')
            // 决策理由：检查是否需要显示电池警告弹窗，避免重复显示
            const dontRemind = localStorage.getItem('batteryAlarmDontRemind') === 'true';
            if (this.BatteryCharging === 2 && !dontRemind && !this.showBatteryAlarmDialog) {
                this.showBatteryAlarmDialog = true;
            }
        },
        checkFirstVisitTips () {
            // 决策理由：检查是否首次访问，如果是则按顺序显示两个tip
            const tipsShownFlag = localStorage.getItem('homePageTipsShown');
            const hasShownTips = tipsShownFlag === 'true';

            console.log('检查首次访问提示:', { tipsShownFlag, hasShownTips });

            if (!hasShownTips) {
                console.log('首次访问，显示提示');
                localStorage.setItem('homePageTipsShown', 'true');
                this.showNextTip();
            } else {
                console.log('非首次访问，跳过提示');
            }
        },
        showNextTip () {
            // 决策理由：先滚动到tip位置，再同时显示两个tip，避免滚动时触摸事件隐藏tip

            // 先滚动到第一个tip位置
            setTimeout(() => {
                if (this.$refs.screenOnControlBar && this.$refs.screenOnControlBar.$el) {
                    this.$refs.screenOnControlBar.$el.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }, 500);

            // 滚动完成后同时显示两个tip
            setTimeout(() => {
                if (this.$refs.screenOnControlBar) {
                    this.$refs.screenOnControlBar.showTipInfo();
                }
                if (this.$refs.timerControlBar) {
                    this.$refs.timerControlBar.showTipInfo();
                }
            }, 1500);
        }
    },
    watch: {
        ScreenModeDelayTime (newVal, oldVal) {
            console.log("ScreenModeDelayTime changed:", oldVal, "->", newVal);
            console.log("新的ScreenOnPickerIndex:", this.ScreenOnPickerIndex);
        },
        CommonFaultDetectionCode (val) {
            if (val == 0 && this.BatteryAlarm == 0) {
                this.$refs.alertMessage.dismiss();
            } else {
                this.$nextTick(() => {
                    this.$refs.alertMessage.show(this.$t('CommonFaultDetectionCode_' + val));
                })

            }
        },
        BatteryAlarm (val) {
            if (val === 0 && this.CommonFaultDetectionCode == 0) {
                this.$refs.alertMessage.dismiss();
            } else {
                this.checkBatteryAlarm();
                this.$nextTick(() => {
                    this.$refs.alertMessage.show(this.$t('BatteryAlarm_' + val));
                });

            }
        },
    }
};
</script>
<style lang="less" scoped>
@import url("../style/Home.less");
</style>
