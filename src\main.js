/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>@talkweb.com.cn
 * @Date: 2025-06-11 10:40:31
 * @LastEditTime: 2025-06-11 10:54:31
 * @LastEditors: <EMAIL>
 */
// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.

import Vue from 'vue';
import App from './App';
import router from './router';
import store from './store';
import i18n from './i18n/index';
import initApi from '@/api/initApi.js';
import twcui from '../lib/index'
import { Tab, Tabs, List, PullRefresh, Circle, Calendar, DatetimePicker, Popup } from 'vant';
import 'vant/lib/index.css';
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(List);
Vue.use(PullRefresh);
Vue.use(Circle);
Vue.use(Calendar);
Vue.use(DatetimePicker);
Vue.use(Popup);
Vue.use(twcui)
import moment from 'moment'
var momentDurationFormatSetup = require("moment-duration-format");
momentDurationFormatSetup(moment);
Vue.prototype.$moment = moment;

// 初始化原型方法等
initApi();
/* eslint-disable */
new Vue({
    el: "#app",
    router,
    store,
    i18n,
    components: { App },
    template: "<App/>"
});
