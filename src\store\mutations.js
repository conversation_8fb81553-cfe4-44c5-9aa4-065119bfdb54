import profile from './profile';
import {handleState} from '../util/index';

const mutations = {
    // desc: 提交非hilink数据  devId
    commitAppData(state, obj) {
        for (let key in obj) {
            if (state.hasOwnProperty(key)) {
                state[key] = obj[key];
            }
        }
    },
    // desc: 提交hilink格式的数据 profile { netInfo:{}}
    // note: param只能是 object 或是 array[object]
    commitMock(state, param) {
        const simulateData = dataObj => {
            for (let sid in dataObj) {
                if (!state.hasOwnProperty(sid)) {
                    continue;
                }
                for (let cha in dataObj[sid]) {
                    if (!state[sid].hasOwnProperty(cha)) {
                        continue;
                    }
                    state[sid][cha] = dataObj[sid][cha];
                }
            }
        };
        if (Ag.isArray(param)) {
            for (let obj of param) {
                Ag.isObject(obj) && simulateData(obj);
            }
        } else if (Ag.isObject(param)) {
            simulateData(param);
        }
    },
    // hilink数据结构的提交
    commitHilink(state, obj) {
        handleState(profile, state, obj);
    }
};

export default mutations;
