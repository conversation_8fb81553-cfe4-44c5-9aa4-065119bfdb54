<!--
 * @Author: Augment Agent
 * @Date: 2025-07-09
 * @LastEditTime: 2025-07-09
 * @LastEditors: Augment Agent
 * @Description: 豪恩服务热线页面
-->
<template>
  <div id="service-hotline-page">
    <Titlebar :title="$t('service_hotline_page_title')" :showRightIcon="false" @leftClick="$router.goBack()" />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      <div class="module-box">
        <ListContainer style="width: auto; margin: 0; padding: 0;">
          <div class="service-hotline-item" @click="callServicePhone">
            <div class="left">
              <div class="phone-number">{{ $t('service_phone_number') }}</div>
              <div class="working-hours">{{ $t('service_working_hours') }}</div>
            </div>
            <div class="right">
              <img class="icon" :src="require('../assets/ic_Timer_on.png')" alt="电话图标" />
            </div>
          </div>
        </ListContainer>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import { goBack } from '../util/mixins';

export default {
  name: 'ServiceHotline',
  mixins: [goBack],
  data() {
    return {
      dialogList: [] // 没有弹窗，空数组
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight']),
    ...mapState(['appMinVersion', 'servicePhone'])
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
    // 呼叫售后电话
    callServicePhone() {
      setTimeout(() => {
        if (
          this.$store.dispatch(
            "isLaterAppVersion",
            this.appMinVersion
          )
        ) {
          console.log("this.appMinVersion", this.appMinVersion);
          window.location.href = `tel:${this.servicePhone}`;
        }
      }, 300);
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/ServiceHotline.less");
</style>
