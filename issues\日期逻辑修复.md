# 日期逻辑修复任务

## 任务背景
修复周报和月报的日期逻辑问题，实现基于日报选择日期的智能切换功能。

## 问题描述

### 原始问题
1. **周报日期逻辑问题**：切换到周报时总是重置为当前周，而不是基于日报选择的日期计算对应的周
2. **月报日期逻辑问题**：切换到月报时总是重置为当前月，而不是基于日报选择的日期计算对应的月
3. **Tab切换时的日期重置**：`toggle()` 方法中调用 `initializeDates()` 会重置所有日期状态

### 期望行为
- 从日报切换到周报：显示日报当前选择日期所在的那一周
- 从日报切换到月报：显示日报当前选择日期所在的那一个月
- 在各报表中重新选择日期：正确更新对应的周期范围
- 保持不同报表类型之间的日期状态同步

## 修复方案

### 实施方案：基于当前选中日期的智能切换

## 具体修改

### 1. SubHome.vue 修改

#### 修改 `toggle()` 方法
- **原逻辑**：切换tab时调用 `initializeDates()` 重置所有日期
- **新逻辑**：调用 `calculateDateForTab()` 进行智能日期计算

#### 新增 `calculateDateForTab()` 方法
- **功能**：根据当前选中的日期智能计算对应的周期
- **逻辑**：
  - 从日报切换：使用日报选中的日期作为基准
  - 从周报切换：使用周报开始日期作为基准
  - 从月报切换：使用月报选中日期作为基准
  - 根据目标tab类型计算对应的日期范围

#### 优化 `getDefaultValue()` 方法
- **增强**：确保周报返回有效的日期范围
- **容错**：如果周范围无效，基于当前日报日期重新计算

#### 增强 `confirmDatePicker()` 方法
- **改进**：同步更新临时状态变量
- **确保**：日期选择后状态一致性

### 2. DialogDatePicker.vue 检查
- **确认**：现有的初始化逻辑已经能够正确处理传入的默认值
- **验证**：周报模式的日期范围计算逻辑正确

## 核心逻辑

### 日期计算规则
1. **日期 → 周范围**：使用 `getStartOfWeek()` 计算指定日期所在周的开始和结束日期
2. **日期 → 月份**：提取指定日期的年月信息，设置为该月第一天
3. **状态同步**：确保选中状态和临时状态的一致性

### 切换逻辑
- **日报 → 周报**：计算日报选中日期所在的周
- **日报 → 月报**：计算日报选中日期所在的月
- **周报 → 日报**：使用周的开始日期
- **月报 → 日报**：使用月份的第一天
- **其他切换**：基于相应的基准日期进行计算

## 预期效果

### 用户体验改进
1. **连续性**：切换报表类型时日期选择具有连续性
2. **直观性**：用户可以直观地看到不同时间维度的数据
3. **一致性**：日期状态在不同报表类型间保持同步

### 功能验证
- ✅ 从日报切换到周报显示正确的周范围
- ✅ 从日报切换到月报显示正确的月份
- ✅ 在各报表中重新选择日期后正确更新
- ✅ 日期选择器传递正确的默认值
- ✅ 状态同步和数据刷新正常工作

## 技术细节

### 关键方法
- `calculateDateForTab(previousIndex, newIndex)` - 智能日期计算
- `getDefaultValue()` - 获取正确的默认值
- `confirmDatePicker(result)` - 确认日期选择并同步状态

### 日期工具函数
- `getStartOfWeek(date)` - 获取周开始日期（周一）
- 日期范围验证和边界检查
- 临时状态和选中状态的同步机制

## 优化更新 - 统一单日选择模式

### 新增优化（基于用户反馈）

#### 问题分析
1. **周报选择复杂**：原来需要理解"选择一周"的概念，用户体验不直观
2. **月报强制月初**：丢失了用户选择的具体日期信息
3. **交互不一致**：三种模式使用不同的选择逻辑

#### 优化方案：统一单日选择模式
- **所有报表类型都使用单日选择**：用户始终只需点击一个日期
- **系统自动计算时间范围**：
  - 日报：显示选中日期的数据
  - 周报：显示选中日期所在周的数据
  - 月报：显示选中日期所在月的数据

#### 具体修改
1. **DialogDatePicker.vue**：
   - 周报模式改为 `mode="single"`
   - 新增 `tempSelectedWeekDate` 属性
   - 新增 `onWeekDateSelect` 方法
   - 修改月报逻辑，保持具体日期

2. **SubHome.vue**：
   - 新增 `selectedWeekDate` 属性
   - 修改 `getDefaultValue` 方法
   - 优化周报导航逻辑
   - 更新数据传递和状态同步

#### 用户体验改进
- ✅ 统一的交互方式：所有模式都是点击单个日期
- ✅ 保持日期信息：月报不再强制转换为月初
- ✅ 直观的反馈：只高亮一个日期，系统自动计算范围
- ✅ 简化的操作：用户无需理解不同的选择模式

## 测试建议

### 功能测试
1. 在日报选择特定日期，切换到周报验证显示的周范围
2. 在日报选择特定日期，切换到月报验证显示的月份
3. 在周报/月报中重新选择日期，验证状态更新
4. 测试边界情况（月末、年末等）
5. 验证日期导航功能的正确性
6. **新增**：验证所有模式都只高亮一个日期
7. **新增**：验证月报保持具体日期信息

### 回归测试
1. 确保原有的日期选择功能正常
2. 验证日期格式化显示正确
3. 检查数据刷新机制工作正常
4. **新增**：确保数据获取逻辑正确处理新的日期格式
