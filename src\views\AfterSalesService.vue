<template>
  <div id="after-sales-service-page">
    <Titlebar :title="$t('after_sales_service')" :showRightIcon="false" @leftClick="$router.goBack()" />
    <div class="content" :style="{ paddingTop: `${statusBarHeight}px` }">
      <!-- 产品信息区域 -->
      <div class="product-info-section">
        <div class="product-info-card">
          <img src="../assets/product.png" style="width: 4.8rem; height: 4.8rem;" alt="">
          <div class="info" style="display: flex;flex-direction: column;margin-left: 0.8rem;">
            <div class="product-name">{{ $t('product_full_name') }}</div>
            <div class="product-serial">{{ $t('serial_number') }}{{ deviceSerialNumber }}</div>
          </div>
        </div>
      </div>

      <!-- 二维码区域 -->
      <div class="qrcode-section">
        <div class="qrcode-card">
          <div class="barcode-container">
            <canvas ref="barcodeCanvas"></canvas>
          </div>
          <div class="serial-text">{{ deviceSerialNumber }}</div>
          <div class="qrcode-title">{{ $t('scan_device_info') }}</div>
          <div class="qrcode-subtitle">{{ $t('scan_device_info_desc') }}</div>
        </div>
      </div>

      <!-- 咨询服务区域 -->
      <div class="consultation-section">
        <div class="section-title">{{ $t('consultation') }}</div>
        <ListContainer style="width: auto">
          <div class="service-item" @click="handleNavClick('hotline')">
            <div class="service-icon">
              <img :src="require('../assets/ic_Timer_on.png')" alt="服务热线" />
            </div>
            <div class="service-content">
              <div class="service-name">{{ $t('service_hotline') }}</div>
              <div class="service-desc">{{ $t('hotline_desc') }}</div>
            </div>
            <div class="service-arrow"></div>
            <div class="service-divider"></div>
          </div>


          <div class="service-item" @click="handleNavClick('online_service')">
            <div class="service-icon">
              <img :src="require('../assets/ic_ScreenOn_on.png')" alt="在线客服" />
            </div>
            <div class="service-content">
              <div class="service-name">{{ $t('online_customer_service') }}</div>
              <div class="service-desc">{{ $t('online_service_desc') }}</div>
            </div>
            <div class="service-arrow"></div>
            <div class="service-divider"></div>
          </div>


          <div class="service-item" @click="handleNavClick('service_account')">
            <div class="service-icon">
              <img :src="require('../assets/icon_service_on.png')" alt="服务号" />
            </div>
            <div class="service-content">
              <div class="service-name">{{ $t('smart_curtain_service') }}</div>
            </div>
            <div class="service-arrow"></div>
          </div>
        </ListContainer>
      </div>
    </div>

    <!-- 第三方服务确认弹窗 -->
    <div class="dialog" @click="cancelThirdPartyService" v-if="isShowThirdPartyDialog">
      <div class="dialog-container">
        <h3 v-if="$t('third_party_service_title') != ''" class="title" style="max-lines: 2;">{{
          $t('third_party_service_title') }}</h3>
        <p :class="$t('third_party_service_title') != '' ? '' : 'message'" v-html="$t('third_party_service_message')"></p>
        <div class="dialog-btns">
          <div @click="cancelThirdPartyService">
            <p>{{ $t('cancel') }}</p>
          </div>
          <span class="line"></span>
          <div @click='confirmThirdPartyService'>
            <p>{{ $t('confirm') }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';
import JsBarcode from 'jsbarcode';
import { goBack } from '../util/mixins';

export default {
  name: 'AfterSalesService',
  mixins: [goBack],
  data () {
    return {
      dialogList: ['isShowThirdPartyDialog'], // 添加第三方服务确认弹窗
      isShowThirdPartyDialog: false // 第三方服务确认弹窗状态
    };
  },
  computed: {
    ...mapGetters(['statusBarHeight']),
    ...mapState(['sn', 'devName', 'deviceModel']),
    deviceSerialNumber () {
      return this.sn || '550425252558004A'; // 使用真实序列号，如果没有则使用默认值
    }
  },
  methods: {
    // goBack方法由mixin提供，移除重复定义
    handleNavClick (type) {
      console.log('咨询服务点击:', type);
      switch (type) {
        case 'hotline':
          // 跳转到豪恩服务热线页面
          this.$router.push('/ServiceHotline');
          break;
        case 'online_service':
          // 显示第三方服务确认弹窗
          this.isShowThirdPartyDialog = true;
          break;
        case 'service_account':
          // 跳转到豪恩服务号页面
          this.$router.push('/ServiceAccount');
          break;
        default:
          break;
      }
    },
    generateBarcode () {
      // 等待DOM更新后生成条形码
      this.$nextTick(() => {
        if (this.$refs.barcodeCanvas && this.deviceSerialNumber) {
          try {
            JsBarcode(this.$refs.barcodeCanvas, this.deviceSerialNumber, {
              format: "CODE128",
              width: 2,
              height: 188,
              displayValue: false,
              background: "transparent",
              lineColor: this.$store.state.isDarkMode ? "#ffffff" : "#000000"
            });
          } catch (error) {
            console.error('生成条形码失败:', error);
          }
        }
      });
    },
    openExternalLink (url) {
      // 尝试使用hilink接口打开外部链接
      if (window.hilink && window.hilink.openUrl) {
        window.hilink.openUrl(url);
      } else if (window.open) {
        // 备用方案：使用window.open
        window.open(url, '_blank');
      } else {
        // 最后备用方案：修改当前页面location
        window.location.href = url;
      }
    },

    // 确认第三方服务跳转
    confirmThirdPartyService () {
      this.isShowThirdPartyDialog = false;
      // 用户点击确认，打开链接
      this.openExternalLink('https://u.y63.cn/37q2');
    },

    // 取消第三方服务跳转
    cancelThirdPartyService () {
      this.isShowThirdPartyDialog = false;
      // 用户点击取消，不做任何操作
      console.log('用户取消跳转');
    }
  },
  mounted () {
    this.generateBarcode();
  },
  watch: {
    deviceSerialNumber () {
      this.generateBarcode();
    }
  }
};
</script>

<style lang="less" scoped>
@import url("../style/AfterSalesService.less");

.title {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow: visible !important;
  text-overflow: unset !important;
  height: auto !important;
  line-height: 2.6rem !important;
  padding: 1.5rem 2.4rem 0.8rem 2.4rem !important;
}

.message {
  font-size: 1.4rem !important;
  color: var(--emui_text_secondary) !important;
  line-height: 2.2rem !important;
  margin: 0px 2.4rem !important;
  padding-bottom: 0.8rem !important;
}
.dialog {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
</style>
