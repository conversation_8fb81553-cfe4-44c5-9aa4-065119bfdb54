/**
 * 日历工具函数
 */

/**
 * 获取一周的开始日期（周一）
 * @param {Date} date 
 * @returns {Date}
 */
export function getStartOfWeek(date) {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    return new Date(d.setDate(diff));
}

/**
 * 获取一周的结束日期（周日）
 * @param {Date} date 
 * @returns {Date}
 */
export function getEndOfWeek(date) {
    const startOfWeek = getStartOfWeek(date);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    return endOfWeek;
}

/**
 * 获取月份的第一天
 * @param {Date} date 
 * @returns {Date}
 */
export function getMonthStart(date) {
    const d = new Date(date);
    return new Date(d.getFullYear(), d.getMonth(), 1);
}

/**
 * 获取月份的最后一天
 * @param {Date} date 
 * @returns {Date}
 */
export function getMonthEnd(date) {
    const d = new Date(date);
    return new Date(d.getFullYear(), d.getMonth() + 1, 0);
}

/**
 * 判断两个日期是否是同一天
 * @param {Date} date1 
 * @param {Date} date2 
 * @returns {boolean}
 */
export function isSameDay(date1, date2) {
    if (!date1 || !date2) return false;
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
}

/**
 * 判断两个日期是否是同一周
 * @param {Date} date1 
 * @param {Date} date2 
 * @returns {boolean}
 */
export function isSameWeek(date1, date2) {
    if (!date1 || !date2) return false;
    const start1 = getStartOfWeek(date1);
    const start2 = getStartOfWeek(date2);
    return isSameDay(start1, start2);
}

/**
 * 判断两个日期是否是同一月
 * @param {Date} date1 
 * @param {Date} date2 
 * @returns {boolean}
 */
export function isSameMonth(date1, date2) {
    if (!date1 || !date2) return false;
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth();
}

/**
 * 格式化日期
 * @param {Date} date 
 * @param {string} format 
 * @returns {string}
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    switch (format) {
        case 'YYYY-MM-DD':
            return `${year}-${month}-${day}`;
        case 'YYYY年MM月DD日':
            return `${year}年${month}月${day}日`;
        case 'YYYY年MM月':
            return `${year}年${month}月`;
        case 'MM月DD日':
            return `${month}月${day}日`;
        default:
            return `${year}-${month}-${day}`;
    }
}

/**
 * 格式化日期范围
 * @param {Date} startDate 
 * @param {Date} endDate 
 * @returns {string}
 */
export function formatDateRange(startDate, endDate) {
    if (!startDate || !endDate) return '';
    
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // 如果是同一年
    if (startYear === endYear) {
        // 如果是同一月
        if (startMonth === endMonth) {
            // 如果是同一天
            if (startDay === endDay) {
                return `${startYear}年${startMonth}月${startDay}日`;
            } else {
                return `${startYear}年${startMonth}月${startDay}日-${endDay}日`;
            }
        } else {
            return `${startYear}年${startMonth}月${startDay}日-${endMonth}月${endDay}日`;
        }
    } else {
        return `${startYear}年${startMonth}月${startDay}日-${endYear}年${endMonth}月${endDay}日`;
    }
}

/**
 * 获取指定日期所在周的范围
 * @param {Date} date 
 * @returns {Array<Date>}
 */
export function getWeekRange(date) {
    const startOfWeek = getStartOfWeek(date);
    const endOfWeek = getEndOfWeek(date);
    return [startOfWeek, endOfWeek];
}

/**
 * 添加天数
 * @param {Date} date 
 * @param {number} days 
 * @returns {Date}
 */
export function addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
}

/**
 * 添加月份
 * @param {Date} date 
 * @param {number} months 
 * @returns {Date}
 */
export function addMonths(date, months) {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
}

/**
 * 获取今天的日期
 * @returns {Date}
 */
export function getToday() {
    return new Date();
}

/**
 * 重置时间为00:00:00
 * @param {Date} date 
 * @returns {Date}
 */
export function resetTime(date) {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
}

/**
 * 判断日期是否在范围内
 * @param {Date} date 
 * @param {Date} minDate 
 * @param {Date} maxDate 
 * @returns {boolean}
 */
export function isDateInRange(date, minDate, maxDate) {
    if (!date) return false;
    
    const resetDate = resetTime(date);
    
    if (minDate) {
        const resetMinDate = resetTime(minDate);
        if (resetDate < resetMinDate) return false;
    }
    
    if (maxDate) {
        const resetMaxDate = resetTime(maxDate);
        if (resetDate > resetMaxDate) return false;
    }
    
    return true;
}

/**
 * 获取两个日期之间的天数差
 * @param {Date} date1 
 * @param {Date} date2 
 * @returns {number}
 */
export function getDaysDiff(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数
    const firstDate = resetTime(date1);
    const secondDate = resetTime(date2);
    
    return Math.round(Math.abs((firstDate - secondDate) / oneDay));
}

/**
 * 克隆日期
 * @param {Date} date 
 * @returns {Date}
 */
export function cloneDate(date) {
    return new Date(date.getTime());
}
