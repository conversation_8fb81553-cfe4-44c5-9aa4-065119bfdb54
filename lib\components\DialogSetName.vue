<template>
  <div class="dialog" @click.self="cancel(false)">
    <div ref="dialogContainerRef" class="dialog-container">
      <h3 id="com.huawei.smarthome:id/dialog_setname_title" class="title">{{ $t('set_name_title') }}</h3>
      <div class="input-icon">
        <input ref="inputRef" :class="errorMsg ? 'text-error' : 'text'" type="text" @focus="focus" v-model="value"
          :placeholder="placeholder">
        <img v-if="value != ''" @click.stop="handleDelete" class="icon" :src="deleteIcon" alt="">
      </div>
      <div v-if="errorMsg" class="error">{{ msg }}</div>
      <div class="dialog-btns">
        <div id="com.huawei.smarthome:id/dialog_setname_cancel" @click.stop='cancel(true)'>
          <p>{{ $t('cancel') }}</p>
        </div>
        <span class="line"></span>
        <div id="com.huawei.smarthome:id/dialog_setname_confirm" @click.stop='confirm'>
          <p>{{ $t('ok') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DialogSetName',
  props: {
    defaultValue: {
      default: '',
      type: String
    },
    placeholder: {
      default: '',
      type: String
    },
  },
  data() {
    return {
      errorMsg: false,
      msg: '',
      value: ''
    }
  },
  watch: {
    value(val) {
      const reg = new RegExp(/[^A-Za-z0-9\u4e00-\u9fa5\/?\s]/g)
      if (val === '') {
        this.errorMsg = false
      }
      if (!reg.test(val)) {
        this.errorMsg = false
      }
    }
  },
  mounted() {
    this.value = this.defaultValue
  },
  computed: {
    deleteIcon() {
      return require(`../assets/${this.$store.getters.imgPath}ic_close.png`);
    }
  },
  methods: {
    focus() {
      let offsetTop = this.$refs.dialogContainerRef.offsetTop
      let u = navigator.userAgent
      let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
      if (isIOS) {
        setTimeout(() => {
          window.scrollTo(0, Math.max(offsetTop - 1, 0))
        }, 200)
      }
    },
    handleDelete() {
      this.value = ''
    },
    confirm() {
      const reg = new RegExp(/[^A-Za-z0-9\u4e00-\u9fa5\/?\s]/g)
      if (this.value.trim().length === 0 && this.value != '') {
        this.errorMsg = true
        this.msg = this.$t('set_name_msg3')
        return
      }
      if (reg.test(this.value)) {
        this.errorMsg = true
        this.msg = this.$t('set_name_msg1')
        return
      }
      if (this.value.length > 16) {
        this.$store.dispatch('toast', this.$t('set_name_msg2'))
        return
      }
      this.errorMsg = false
      this.$emit('confirm', this.value)
    },
    cancel(check) {
      this.errorMsg = false
      this.$emit('cancel', check)
    }
  },
}
</script>

<style lang="less" scoped>
@import url("../style/public.less");

.input-icon {
  width: calc(100% - 4.8rem);
  margin: 0.8rem 2.4rem;
  position: relative;
}

.icon {
  position: absolute;
  flex-shrink: 0;
  width: 1.6rem;
  height: 1.6rem;
  right: 0;
  top: 1.6rem;
}

.text {
  width: calc(100% - 1.6rem);
  height: 4.8rem;
  font-size: 1.6rem;
  border: 0;
  outline: none;
  padding-right: 1.6rem;
  border-bottom: 1px solid var(--emui_color_list_divider_light);
  background-color: var(--emui_popup_bg);
  color: var(--emui_text_primary);
}

.text::placeholder {
  color: var(--emui_text_secondary)
}

.text:focus {
  outline: none;
  border-bottom: 1px solid var(--emui_text_secondary);
  caret-color: var(--emui_accent);
}

.text-error {
  width: 100%;
  height: 4.8rem;
  font-size: 1.6rem;
  border: 0;
  outline: none;
  border-bottom: 1px solid var(--emui_functional_red);
  background-color: var(--emui_popup_bg);
  color: var(--emui_text_primary);
}

.text-error::placeholder {
  color: var(--emui_text_secondary)
}

.text-error:focus {
  outline: none;
  border-bottom: 1px solid var(--emui_functional_red);
  caret-color: var(--emui_accent);
}

.error {
  font-size: 1.2rem;
  margin-left: 2.4rem;
  color: var(--emui_functional_red);
}
</style>