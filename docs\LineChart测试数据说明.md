# LineChart 柱状图测试数据说明

## 概述
为了验证LineChart组件的柱状图重构功能，我们添加了完整的模拟数据生成系统。这个系统可以生成符合真实接口格式的测试数据，用于验证各种报表类型的显示效果和交互功能。

## 功能特性

### 1. 模拟数据生成
- **数据格式**：完全符合 `getDevHistory` 接口返回的数据格式
- **传感器支持**：支持 CO2、甲醛、温度、湿度 四种传感器类型
- **时间范围**：根据不同报表类型生成相应时间范围的数据
- **数值变化**：包含趋势变化和随机噪声，模拟真实传感器数据

### 2. 传感器数据配置

| 传感器类型 | SID | Character | 数值范围 | 单位 |
|-----------|-----|-----------|----------|------|
| CO2 | co2 | current | 300-600 | ppm |
| 甲醛 | hcho | current | 40-150 | mg/m³ |
| 温度 | temperature | current | 15-35 | ℃ |
| 湿度 | humidity | current | 30-60 | % |

### 3. 报表类型数据密度

| 报表类型 | 数据间隔 | 数据点数量 | 说明 |
|----------|----------|------------|------|
| 日报 | 1小时 | 24个 | 24小时内每小时一个数据点 |
| 周报 | 4小时 | 42个 | 7天内每4小时一个数据点 |
| 月报 | 1天 | 30个 | 30天内每天一个数据点 |
| 年报 | 1周 | 52个 | 12个月内每周一个数据点 |

## 使用方法

### 1. 开启测试模式
有两种方式开启测试控制面板：

**方式一：开发环境自动显示**
```bash
npm run dev
# 开发环境下会自动显示测试按钮
```

**方式二：手动开启**
```javascript
// 在浏览器控制台执行
localStorage.setItem('showTestControls', 'true');
// 刷新页面后会显示测试按钮
```

### 2. 测试按钮功能
页面右下角会显示两个测试按钮：

- **模拟数据/真实数据**：切换数据源模式
  - 蓝色：当前使用模拟数据
  - 灰色：当前使用真实接口数据
- **生成测试数据**：为所有传感器类型生成测试数据

### 3. 编程接口
```javascript
// 切换模拟数据模式
this.toggleMockDataMode();

// 生成所有传感器的测试数据
this.generateAllMockData();

// 为特定传感器生成数据
const mockData = this.generateMockData(sensorType, timeRange);
```

## 测试场景

### 1. 柱状图样式测试
- 验证双端圆角效果
- 验证不同报表类型的柱子宽度
- 验证颜色和透明度

### 2. 时间轴测试
- **日报**：验证24小时标签显示（每6小时显示一个）
- **周报**：验证7天标签全部显示
- **月报**：验证30天标签选择性显示（每5天一个，旋转30度）
- **年报**：验证12个月标签全部显示

### 3. 交互功能测试
- 点击柱状图验证指示线显示
- 验证数据点选中和高亮效果
- 验证数值显示区域的更新

### 4. 数值显示测试
- **日报**：验证显示选中数据的平均值
- **周报/月报/年报**：验证显示最小值~最大值范围
- 验证不同传感器类型的数值格式化

## 数据格式示例

### 接口返回格式
```json
{
    "totalCount": 24,
    "pageNo": 0,
    "pageSize": 1000,
    "list": [
        {
            "devId": "mock_device_001",
            "gatewayId": "mock_gateway_001",
            "sid": "co2",
            "data": {
                "current": 450.25
            },
            "timestamp": "20241227T120000Z"
        }
    ]
}
```

### 聚合后的图表数据格式
```javascript
// 区间数据（周报/月报/年报）
[
    { min: 420.15, max: 480.75 },
    { min: 435.20, max: 495.80 },
    null // 无数据的时间段
]

// 单值数据（日报）
[450.25, 465.30, 440.15, null]
```

## 调试信息
开启模拟数据模式后，控制台会输出详细的调试信息：

```
生成 co2 模拟数据 - 报表类型: 0, 时间范围: 20241227T000000Z ~ 20241227T235959Z
co2 模拟数据: {totalCount: 24, pageNo: 0, pageSize: 1000, list: Array(24)}
co2 聚合后数据: [450.25, 465.30, 440.15, ...]
```

## 注意事项

1. **性能考虑**：模拟数据生成是实时的，大量数据可能影响性能
2. **数据一致性**：每次生成的模拟数据都不同，包含随机成分
3. **时间同步**：确保系统时间正确，影响时间范围计算
4. **浏览器兼容**：使用了ES6+语法，需要现代浏览器支持

## 故障排除

### 常见问题及解决方案

#### 1. 柱状图不显示
**症状**：LineChart组件区域空白，没有柱状图显示
**可能原因**：
- 数据格式不匹配（日报应该是数值数组，周报/月报/年报应该是区间对象数组）
- ECharts配置错误
- chartData为空或undefined

**调试步骤**：
```javascript
// 1. 检查数据格式
const vm = document.querySelector('#subhome-page').__vue__;
vm.debugDataState(); // 查看完整数据状态

// 2. 检查特定传感器数据
console.log('CO2数据:', vm.chartData.co2);
console.log('数据类型:', typeof vm.chartData.co2?.[0]);

// 3. 强制重新渲染图表
vm.fetchDevHistory();
```

#### 2. 数值显示异常
**症状**：页面上方的数值显示区域显示错误或不更新
**可能原因**：
- selectedChartData未正确更新
- getDisplayValueText方法逻辑错误
- 事件传递问题

**调试步骤**：
```javascript
// 检查选中数据
console.log('选中的图表数据:', vm.selectedChartData);

// 手动触发数据选中事件
vm.onChartValueSelected({
    name: '12:00',
    value: 450 // 或 { min: 420, max: 480 }
});
```

#### 3. 测试按钮不显示
**解决方案**：
```javascript
// 方法1：设置localStorage
localStorage.setItem('showTestControls', 'true');
location.reload();

// 方法2：检查开发环境
console.log('NODE_ENV:', process.env.NODE_ENV);
```

#### 4. 模拟数据生成失败
**调试步骤**：
```javascript
// 检查传感器配置
console.log('传感器配置:', vm.sensorConfig);

// 检查时间范围
const timeRange = vm.getTimeRangeUTC();
console.log('时间范围:', timeRange);

// 手动生成单个传感器数据
const mockData = vm.generateMockData(2, timeRange); // CO2
console.log('生成的模拟数据:', mockData);
```

### 数据格式验证

#### 正确的数据格式
```javascript
// 日报数据（数值数组）
[450.25, 465.30, 440.15, 455.80, ...]

// 周报/月报/年报数据（区间对象数组）
[
    { min: 420.15, max: 480.75 },
    { min: 435.20, max: 495.80 },
    null, // 无数据时间段
    ...
]
```

#### 数据验证方法
```javascript
// 验证数据格式
function validateChartData(data, reportType) {
    if (!Array.isArray(data)) return false;

    if (reportType === 0) {
        // 日报：应该是数值数组
        return data.every(item => item === null || typeof item === 'number');
    } else {
        // 其他：应该是区间对象数组
        return data.every(item =>
            item === null ||
            (typeof item === 'object' && 'min' in item && 'max' in item)
        );
    }
}
```

### 调试步骤
1. 打开浏览器开发者工具
2. 启用测试控制面板：`localStorage.setItem('showTestControls', 'true')`
3. 刷新页面，点击"调试数据"按钮
4. 查看控制台输出的详细信息
5. 验证数据格式和组件状态
6. 检查Network面板的接口调用（如果使用真实数据）

## 控制台测试示例

### 快速测试命令
```javascript
// 1. 开启测试控制面板
localStorage.setItem('showTestControls', 'true');
location.reload();

// 2. 访问Vue组件实例（在SubHome页面）
const vm = document.querySelector('#subhome-page').__vue__;

// 3. 切换到模拟数据模式
vm.useMockData = true;

// 4. 测试不同报表类型
vm.selectIndex = 0; // 日报
vm.fetchDevHistory();

vm.selectIndex = 1; // 周报
vm.fetchDevHistory();

vm.selectIndex = 2; // 月报
vm.fetchDevHistory();

vm.selectIndex = 3; // 年报
vm.fetchDevHistory();

// 5. 查看生成的图表数据
console.log('当前图表数据:', vm.chartData);

// 6. 生成所有传感器的测试数据
vm.generateAllMockData();
```

### 验证数据格式
```javascript
// 检查模拟数据格式
const mockData = vm.generateMockData(2, {
    startTime: '20241227T000000Z',
    endTime: '20241227T235959Z'
});
console.log('模拟数据格式:', mockData);

// 检查聚合后的数据
console.log('CO2聚合数据:', vm.chartData.co2);
console.log('甲醛聚合数据:', vm.chartData.hcho);
```

### 测试交互功能
```javascript
// 模拟图表点击事件
vm.onChartValueSelected({
    name: '12:00',
    value: { min: 420, max: 480 }
});

// 查看选中的数据
console.log('选中的图表数据:', vm.selectedChartData);
```
