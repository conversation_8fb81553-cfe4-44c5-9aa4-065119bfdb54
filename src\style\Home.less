@import url("../../lib/style/public.less");

#page {
    overflow-y: auto;

    .content {
        // pad适配
        margin: 5.6rem var(--home_margin) 0px var(--home_margin);
        position: relative;
    }

    .module-box {
        margin: 0 .6rem;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        align-items: flex-start;
        justify-content: flex-start;
    }

    .status-right {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 2.4rem;

        .electric-img {
            width: 2.4rem;
            height: 2.4rem;
        }

        &>span:nth-of-type(1) {
            font-size: 1.6rem;
            margin-left: 0.2rem;
            line-height: 2.4rem;
            color: var(--emui_text_primary);
        }

        &>span:nth-of-type(2) {
            font-size: 1.2rem;
            line-height: 2.4rem;
            margin-left: 0.1rem;
            color: var(--emui_text_secondary);
        }
    }


    .lrimg {
        width: 2.4rem;
        height: 2.4rem;
        background-repeat: no-repeat;
        background-size: cover;

        &.disabled {
            opacity: .4;
            pointer-events: none;
            cursor: default;
        }
    }

    .color-64BB5C {
        color: var(--emui_color_connected)
    }

    .color-E84026 {
        color: var(--emui_functional_red)
    }

    .color-ED6F21 {
        color: var(--emui_color_warning)
    }

    .color-F7CE00 {
        color: var(--emui_color_F7CE00)
    }

    .color-F9A01E {
        color: var(--emui_color_F9A01E)
    }

    .color-E64566 {
        color: var(--emui_color_E64566)
    }
}

.dark #page {}
