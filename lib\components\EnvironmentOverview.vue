<template>
  <div id="environment-overview">
    <div class="box">
      <div class="left">
        <div class="quality-gauge">
          <svg class="gauge-svg" viewBox="0 0 120 80">
            <!-- 定义渐变 -->
            <defs>
              <linearGradient id="gaugeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color:#64BB5C;stop-opacity:1" />
                <stop offset="50%" style="stop-color:#F7CE00;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#E84026;stop-opacity:1" />
              </linearGradient>
            </defs>
            <!-- 背景弧线 -->
            <path class="gauge-bg" 
                  d="M 20 60 A 40 40 0 0 1 100 60" 
                  fill="none" 
                  stroke="#f0f0f0" 
                  stroke-width="8"/>
            <!-- 完整的彩色弧线 -->
            <path class="gauge-full" 
                  d="M 20 60 A 40 40 0 0 1 100 60"
                  fill="none" 
                  stroke="url(#gaugeGradient)" 
                  stroke-width="8"
                  stroke-linecap="round"/>
            <!-- 指示点 -->
            <circle :cx="indicatorX" 
                    :cy="indicatorY" 
                    r="4" 
                    :fill="gaugeColor"/>
          </svg>
          <div class="quality-text">{{ qualityLevelText }}</div>
        </div>
      </div>

      <div class="right">
        <div class="environment-info">
          <div class="environment-title">{{ $t('current_indoor_environment') }}</div>
          <div class="environment-subtitle">{{ $t('comprehensive_data_reference') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EnvironmentOverview",
  props: {
    co2Current: {
      default: 0,
      type: Number
    },
    co2Level: {
      default: 1,
      type: Number
    },
    hchoCurrent: {
      default: 0,
      type: Number
    },
    temperature: {
      default: 0,
      type: Number
    },
    humidity: {
      default: 0,
      type: Number
    },
    disabled: {
      default: false,
      type: Boolean
    }
  },
  computed: {
    isDarkMode() {
      return this.$store.state.isDarkMode;
    },
    // 计算综合环境质量等级
    qualityLevel() {
      if (this.disabled) return 0;
      
      let score = 0;
      let factors = 0;
      
      // CO2 评分 (0-3分)
      if (this.co2Level <= 1) {
        score += 3;
      } else if (this.co2Level <= 2) {
        score += 2;
      } else {
        score += 1;
      }
      factors++;
      
      // 甲醛评分 (0-3分) - 简化评估，基于常见阈值
      if (this.hchoCurrent <= 0.08) {
        score += 3;
      } else if (this.hchoCurrent <= 0.12) {
        score += 2;
      } else {
        score += 1;
      }
      factors++;
      
      // 温度评分 (0-3分) - 舒适温度范围 18-26°C
      if (this.temperature >= 18 && this.temperature <= 26) {
        score += 3;
      } else if (this.temperature >= 16 && this.temperature <= 30) {
        score += 2;
      } else {
        score += 1;
      }
      factors++;
      
      // 湿度评分 (0-3分) - 舒适湿度范围 40-60%
      if (this.humidity >= 40 && this.humidity <= 60) {
        score += 3;
      } else if (this.humidity >= 30 && this.humidity <= 70) {
        score += 2;
      } else {
        score += 1;
      }
      factors++;
      
      const avgScore = score / factors;
      
      if (avgScore >= 2.5) return 3; // 优
      if (avgScore >= 1.5) return 2; // 良
      return 1; // 差
    },
    qualityLevelClass() {
      return {
        'quality-excellent': this.qualityLevel === 3,
        'quality-good': this.qualityLevel === 2,
        'quality-poor': this.qualityLevel === 1
      };
    },
    qualityLevelText() {
      if (this.disabled) return '--';
      
      switch (this.qualityLevel) {
        case 3: return this.$t('quality_excellent');
        case 2: return this.$t('quality_good');
        case 1: return this.$t('quality_poor');
        default: return '--';
      }
    },
    // 仪表盘颜色
    gaugeColor() {
      switch (this.qualityLevel) {
        case 3: return '#64BB5C'; // 绿色 - 优
        case 2: return '#F7CE00'; // 黄色 - 良  
        case 1: return '#E84026'; // 红色 - 差
        default: return '#CCCCCC';
      }
    },
    // 指示器位置
    indicatorX() {
      if (this.disabled) return 20;
      
      let angle = 0;
      switch (this.qualityLevel) {
        case 1: angle = 30; break;
        case 2: angle = 90; break;
        case 3: angle = 150; break;
      }
      
      const radian = (angle * Math.PI) / 180;
      return 60 + 40 * Math.cos(Math.PI - radian);
    },
    indicatorY() {
      if (this.disabled) return 60;
      
      let angle = 0;
      switch (this.qualityLevel) {
        case 1: angle = 30; break;
        case 2: angle = 90; break;
        case 3: angle = 150; break;
      }
      
      const radian = (angle * Math.PI) / 180;
      return 60 - 40 * Math.sin(Math.PI - radian);
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
};
</script>

<style lang='less' scoped>
@import url("../style/public.less");

#environment-overview {
  width: 100%;
  color: var(--emui_text_primary);
  margin-bottom: 0.8rem;

  .box {
    position: relative;
    height: 8.8rem;
    margin: 0 var(--emui_dimens_default_start);
    display: flex;
    align-items: center;
    .cardStyle();
    cursor: pointer;
    padding: 1.6rem 2.4rem;

    &:active {
      opacity: 0.8;
    }

    .left {
      display: flex;
      align-items: center;
      margin-right: 2.4rem;

      .quality-gauge {
        position: relative;
        width: 8rem;
        height: 5.6rem;

        .gauge-svg {
          width: 100%;
          height: 100%;
        }

        .gauge-bg {
          opacity: 0.2;
        }

        .quality-text {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -30%);
          font-size: 2.4rem;
          font-weight: 600;
          color: var(--emui_text_primary);
        }
      }
    }

    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .environment-info {
        .environment-title {
          font-size: 1.8rem;
          font-weight: 500;
          color: var(--emui_text_primary);
          margin-bottom: 0.8rem;
          line-height: 1.2;
        }

        .environment-subtitle {
          font-size: 1.2rem;
          color: var(--emui_text_secondary);
          line-height: 1.3;
        }
      }
    }
  }

  &.disabled .box {
    opacity: 0.4;
    cursor: default;
  }
}

.pad #environment-overview .box {
  padding: 0px;
}

.tahiti #environment-overview .box {
  padding: 0px;
}
</style>
