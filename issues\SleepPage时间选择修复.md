# SleepPage 时间选择和显示问题修复

## 修复概述
修复了 SleepPage.vue 组件中的时间选择和显示问题，包括时间未设置状态判断、跨日时间显示和保存功能报错等问题。

## 修复的问题

### 1. 时间未设置状态判断问题 ✅
**问题描述：** 选择 0:00 时被错误地识别为"未设置"状态
**修复方案：** 
- 添加 `isTimeNotSet()` 辅助方法，只有当时间为 `null`、`undefined` 或 `""` 时才判断为未设置
- 允许 `0` 作为有效的 0:00 时间

### 2. 跨日时间显示问题 ✅
**问题描述：** 当开始时间晚于结束时间时，没有显示跨日标识
**修复方案：**
- 在 `endInfo` 计算属性中添加跨日判断逻辑
- 当 `startIndex > endIndex` 时，在结束时间前显示"次日"标识
- 支持中英文环境下的显示适配

### 3. 保存功能报错问题 ✅
**问题描述：** 点击保存按钮时，0:00 时间被误判为无效时间
**修复方案：**
- 修改 `saveSleepSettings()` 方法中的时间验证逻辑
- 使用 `isTimeNotSet()` 方法进行准确的时间验证
- 允许 0:00 时间正常保存

### 4. 国际化适配 ✅
**修复方案：**
- 在 `zh.json` 中添加 `"next_day": "次日"` 和 `"time_not_set": "未设置时间"`
- 在 `en.json` 中添加 `"next_day": "Next day"` 和 `"time_not_set": "Time not set"`

## 技术实现细节

### 新增辅助方法
```javascript
// 判断时间是否真正未设置
isTimeNotSet(timeIndex) {
    return timeIndex == null || timeIndex == undefined || timeIndex === "";
}

// 格式化时间显示，支持次日标识
formatTimeDisplay(timeIndex, isNextDay = false) {
    if (this.isTimeNotSet(timeIndex)) {
        return this.$t("no_setting");
    }
    
    let hour = parseInt(timeIndex / 60) >= 10
        ? parseInt(timeIndex / 60)
        : "0" + parseInt(timeIndex / 60);
    let min = timeIndex % 60 >= 10
        ? timeIndex % 60
        : "0" + (timeIndex % 60);
    
    const timeStr = hour + ":" + min;
    return isNextDay ? this.$t("next_day") + " " + timeStr : timeStr;
}
```

### 修改的计算属性
```javascript
startInfo() {
    return this.formatTimeDisplay(this.startIndex);
}

endInfo() {
    // 判断是否为跨日时间：开始时间晚于结束时间
    const isNextDay = !this.isTimeNotSet(this.startIndex) && 
                     !this.isTimeNotSet(this.endIndex) && 
                     this.startIndex > this.endIndex;
    
    return this.formatTimeDisplay(this.endIndex, isNextDay);
}
```

## 测试场景

### 1. 0:00 时间测试
- ✅ 选择开始时间为 0:00，应显示 "00:00" 而不是"未设置"
- ✅ 选择结束时间为 0:00，应显示 "00:00" 而不是"未设置"
- ✅ 保存时 0:00 时间应该正常保存，不报错

### 2. 跨日时间测试
- ✅ 开始时间 22:00，结束时间 6:00，应显示 "开始时间: 22:00，结束时间: 次日 06:00"
- ✅ 开始时间 23:30，结束时间 1:30，应显示 "开始时间: 23:30，结束时间: 次日 01:30"

### 3. 正常时间测试
- ✅ 开始时间 8:00，结束时间 18:00，应显示 "开始时间: 08:00，结束时间: 18:00"（无次日标识）

### 4. 国际化测试
- ✅ 中文环境：显示"次日 06:00"
- ✅ 英文环境：显示"Next day 06:00"

## 修复的额外问题

### 5. Toast 组件使用错误 ✅
**问题描述：** `Toast.show is not a function` 错误
**修复方案：**
- Toast 组件已通过 `Vue.prototype.$toast` 注册为全局方法
- 将 `Toast.show()` 改为 `this.$toast()`
- 移除不必要的 Toast 导入

## 修改的文件
1. `src/i18n/language/zh.json` - 添加中文翻译
2. `src/i18n/language/en.json` - 添加英文翻译
3. `src/views/SleepPage.vue` - 修复时间逻辑和 Toast 使用方式

## 向后兼容性
- ✅ 保持原有的时间格式和显示逻辑
- ✅ 不影响现有的数据存储格式
- ✅ 保持与设备通信协议的兼容性
- ✅ 修复 Toast 组件调用方式，确保提示信息正常显示
