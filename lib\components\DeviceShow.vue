<template>
  <div id="device_show">
    <img class="device_pic" :src="product">
    <img class="logo" :src="logo">
  </div>
</template>

<script>

export default {
  props: {
    product: {
      required: true,
      type: String
    },
    logo: {
      required: true,
      type: String
    },
  },
  name: "DeviceShow"
};
</script>

<style lang="less" scoped>
#device_show {
  width: 100%;
  margin-bottom: 1.0rem;
  position: relative;
  overflow: hidden;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  img {
    display: block;
    opacity: var(--device-opacity);
  }

  .device_pic {
    height: 25.2rem;
    width: 25.2rem;
  }

  .logo {
    height: 1.8rem;
  }
}
</style>
