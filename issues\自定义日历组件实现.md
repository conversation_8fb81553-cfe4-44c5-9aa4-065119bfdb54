# 自定义日历组件实现任务

## 任务背景
用户发现 Vant UI 的 Calendar 组件无法满足具体需求，要求参考开源项目从零开始实现一个自定义的日历组件，并集成到现有的 DialogDatePicker.vue 弹窗中。

## 需求分析
1. **替换 Vant Calendar**：创建完全自定义的日历组件
2. **保持现有接口**：与 SubHome.vue 的兼容性
3. **三种模式支持**：日报（单日选择）、周报（周范围选择）、月报（月份选择）
4. **保持弹窗结构**：DialogDatePicker.vue 的整体结构不变
5. **自定义头部导航**：月份切换、标题、"今天"按钮
6. **样式主题**：保持现有的视觉效果

## 实现方案

### 1. 创建 CustomCalendar.vue 组件
- **文件**：`lib/components/CustomCalendar.vue`
- **功能特性**：
  - 基础日历网格布局（7列 × 6行）
  - 支持单日选择模式（mode="single"）
  - 支持周范围选择模式（mode="week"）
  - 支持范围选择模式（mode="range"）
  - 今天标记和选中状态显示
  - 日期禁用功能
  - 响应式设计

### 2. 创建日历工具函数
- **文件**：`lib/utils/calendar.js`
- **工具函数**：
  - `getStartOfWeek()` - 获取一周的开始日期
  - `getEndOfWeek()` - 获取一周的结束日期
  - `getMonthStart()` - 获取月份的第一天
  - `getMonthEnd()` - 获取月份的最后一天
  - `isSameDay()` - 判断两个日期是否是同一天
  - `formatDate()` - 格式化日期
  - `getWeekRange()` - 获取指定日期所在周的范围
  - 其他日期计算和格式化函数

### 3. 集成到 DialogDatePicker.vue
- **修改内容**：
  - 导入 CustomCalendar 组件
  - 替换日报模式的 van-calendar 为 CustomCalendar
  - 替换周报模式的 van-calendar 为 CustomCalendar
  - 保留月报模式的 van-datetime-picker
  - 更新事件处理方法
  - 增强月份导航功能
  - 添加"今天"按钮点击事件

## 实施结果

### ✅ 已完成的功能：

#### 1. CustomCalendar 组件
- **基础功能**：
  - 7×6 日历网格布局
  - 星期标题显示
  - 当月、上月、下月日期显示
  - 今天标记（小圆点）
  - 选中状态显示

- **选择模式**：
  - `single` 模式：单日选择
  - `week` 模式：周范围选择
  - `range` 模式：自由范围选择

- **Props 接口**：
  - `mode` - 选择模式
  - `currentDate` - 当前显示的年月
  - `selectedDate` - 选中的日期（单选）
  - `selectedRange` - 选中的日期范围
  - `minDate` / `maxDate` - 日期范围限制

- **事件接口**：
  - `@date-select` - 单日选择事件
  - `@week-select` - 周选择事件
  - `@range-select` - 范围选择事件

#### 2. 日历工具函数库
- **完整的日期计算工具**：
  - 周、月份计算函数
  - 日期比较和格式化函数
  - 日期范围验证函数
  - 日期操作函数（添加天数、月份等）

#### 3. DialogDatePicker.vue 集成
- **组件替换**：
  - 日报模式：使用 CustomCalendar（single 模式）
  - 周报模式：使用 CustomCalendar（week 模式）
  - 月报模式：保留 van-datetime-picker

- **功能增强**：
  - 月份导航支持所有模式
  - "今天"按钮功能完整
  - 事件处理机制完善
  - 样式适配和主题支持

### 🎯 解决的问题：
- **Vant Calendar 限制** → ✅ 完全自定义实现
- **功能不足** → ✅ 支持所有需要的选择模式
- **样式限制** → ✅ 完全可控的样式系统
- **交互体验** → ✅ 优化的用户交互

### 📋 技术特点：
- **模块化设计**：组件和工具函数分离
- **可扩展性**：易于添加新的选择模式
- **性能优化**：高效的日期计算和渲染
- **样式一致性**：与现有设计系统完美融合
- **类型安全**：完整的 props 验证

### 🔧 保持的兼容性：
- **Props 接口**：与原有 DialogDatePicker 完全兼容
- **事件接口**：保持原有的事件处理机制
- **样式主题**：使用现有的 CSS 变量系统
- **弹窗结构**：保持原有的弹窗布局和交互

## 测试建议
1. **基础功能测试**：
   - 日历网格正确显示
   - 今天标记正确显示
   - 月份切换功能正常

2. **选择模式测试**：
   - 日报模式：单日选择和确认
   - 周报模式：周范围选择和确认
   - 月报模式：月份选择和确认

3. **交互功能测试**：
   - "今天"按钮跳转功能
   - 月份导航按钮功能
   - 日期点击响应

4. **边界情况测试**：
   - 最小/最大日期限制
   - 跨月份选择
   - 禁用日期处理

5. **样式和响应式测试**：
   - 不同屏幕尺寸适配
   - 主题色彩正确应用
   - 选中状态视觉反馈

## 问题修复记录

### 🐛 修复的问题（2024年6月）：

#### 1. **弹窗事件冒泡问题**
- **问题**：点击任何地方弹窗都关闭
- **原因**：事件冒泡机制导致
- **修复**：
  - 修改弹窗容器的事件处理：`@click="cancel(false)"` + `@click.stop` 在容器上
  - 为月份导航按钮添加 `@click.stop`
  - 为"今天"按钮添加 `@click.stop`
  - 为日历内容区域添加 `@click.stop`

#### 2. **月份导航功能失效**
- **问题**：点击左右箭头无法切换月份
- **原因**：事件冒泡被弹窗关闭事件拦截
- **修复**：添加 `@click.stop` 阻止事件冒泡

#### 3. **日期选中状态不更新**
- **问题**：点击日期后选中颜色不变化
- **原因**：CustomCalendar 组件没有监听 props 变化
- **修复**：
  - 添加对 `selectedDate` 的 watch 监听
  - 添加对 `selectedRange` 的 watch 监听（深度监听）
  - 确保 props 变化时重新生成日历数据

#### 4. **日期文字居中问题**
- **问题**：圆圈内的日期数字没有完全居中
- **原因**：使用 `line-height` 与 `display: flex` 冲突
- **修复**：
  - 选中状态：使用 `display: flex` + `align-items: center` + `justify-content: center`
  - 范围开始/结束状态：同样使用 flex 布局
  - 悬停状态：同样使用 flex 布局
  - 移除 `line-height` 属性

#### 5. **周选择模式范围显示问题**
- **问题**：周选择模式下范围高亮不正确
- **原因**：`isDateInRange` 方法只支持 `range` 模式
- **修复**：扩展 `isDateInRange` 方法支持 `week` 模式

#### 6. **悬停状态优化**
- **问题**：已选中的日期悬停时样式冲突
- **修复**：悬停选择器添加 `:not(.selected)` 排除已选中状态

#### 7. **月份导航逻辑问题（第二轮修复）**
- **问题**：点击上个月日期后再点击"上一月"按钮跳到错误月份
- **原因**：月份导航基于选中的日期而不是显示的月份
- **修复**：
  - 添加独立的 `currentDisplayDate` 状态管理当前显示月份
  - 月份导航基于 `currentDisplayDate` 而不是选中日期
  - 分离"显示月份"和"选中日期"的概念
  - 当选择其他月份日期时自动切换显示月份

#### 8. **选中状态智能管理**
- **优化**：当切换月份时，如果选中的日期不在新月份中，保持选中状态但显示正确月份
- **增强**：点击其他月份日期时自动切换到该月份显示

#### 9. **JavaScript Date 溢出问题（第三轮修复）**
- **问题**：点击5月30号后再点击"上一月"跳到4月份
- **原因**：JavaScript Date 对象的自动调整机制，4月没有30号导致日期溢出
- **修复**：
  - 重写 `navigateMonth` 方法，使用安全的年月计算
  - 所有显示月份设置都使用该月的第一天，避免日期溢出
  - 修复初始化、日期选择、周选择等所有相关方法
  - 确保月份导航始终是连续的，不会因为日期问题跳跃

#### 10. **点击其他月份日期跳转到错误月份问题（第四轮修复）**
- **问题**：在6月份点击5月30号（灰色二级文字）跳转到4月份而不是5月份
- **根本原因**：CustomCalendar 组件中创建上个月日期的逻辑错误
  - `new Date(year, month - 1, 0)` 创建的是上上个月的最后一天
  - 导致5月30号的月份索引是3（4月）而不是4（5月）
- **修复**：
  - 重写上个月日期创建逻辑，正确计算上个月的年份和月份
  - 使用 `new Date(year, month, 0).getDate()` 获取上个月的最后一天
  - 确保上个月日期的月份索引正确
  - 恢复 `onDateSelect` 中的自动月份切换功能（这是用户期望的行为）

### ✅ 修复后的功能状态：
- **月份导航**：✅ 正常工作，基于显示月份而非选中日期
- **日期选择**：✅ 点击日期正确更新选中状态和颜色
- **弹窗交互**：✅ 只有点击背景或取消按钮才关闭弹窗
- **文字居中**：✅ 所有状态下日期数字完全居中
- **颜色层级**：✅ 本月和其他月份颜色区分明显
- **周选择**：✅ 周范围选择和高亮显示正确
- **月份跳跃问题**：✅ 已修复，月份导航逻辑正确
- **智能月份切换**：✅ 点击其他月份日期自动切换显示月份

## 后续优化建议
1. **性能优化**：考虑虚拟滚动支持大范围日期
2. **功能扩展**：添加节假日标记、自定义日期标记等
3. **国际化**：支持不同地区的周开始日期设置
4. **动画效果**：添加月份切换的过渡动画
5. **键盘导航**：支持键盘操作日历
