<!--
 * @Author: Augment Agent
 * @Date: 2025-07-07
 * @LastEditTime: 2025-07-07
 * @LastEditors: Augment Agent
 * @Description: 电池低电量警告弹窗组件
-->
<template>
  <div class="dialog" v-if="visible">
    <div class="dialog-container">
      <h3 class="title">{{ $t('battery_alarm_title') }}</h3>
      <p class="message">{{ $t('battery_alarm_message') }}</p>

      <!-- 下次不再提醒选项 -->
      <div class="checkbox-container">
        <label class="checkbox-label" @click="toggleDontRemind">
          <span class="checkbox-wrapper">
            <input
              type="checkbox"
              class="checkbox-input"
              v-model="dontRemindAgain"
              @click.stop
            >
            <span class="checkbox-core"></span>
          </span>
          <span class="checkbox-text">{{ $t('battery_alarm_dont_remind') }}</span>
        </label>
      </div>

      <!-- 确认按钮 -->
      <div class="dialog-btns">
        <div class="confirm-btn" @click="handleConfirm">
          <p>{{ $t('battery_alarm_confirm') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BatteryAlarmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dontRemindAgain: false
    };
  },
  methods: {
    handleConfirm() {
      // 决策理由：用户点击确认时，需要将"不再提醒"状态传递给父组件
      this.$emit('confirm', this.dontRemindAgain);
    },
    toggleDontRemind() {
      // 决策理由：点击label时切换checkbox状态，提升用户体验
      this.dontRemindAgain = !this.dontRemindAgain;
    }
  }
};
</script>

<style lang="less" scoped>
// 决策理由：使用public.less中定义的弹窗样式规范，确保与项目整体UI一致
.dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--emui_mask_regular);
  z-index: 111;
}

// 决策理由：继承public.less中的.dialog-container样式，实现底部弹窗效果
.dialog-container {
  position: fixed;
  bottom: 1.6rem;
  left: 1.2rem;
  right: 1.2rem;
  background: var(--emui_popup_bg);
  border-radius: 2.4rem;
  z-index: 111;
  transition: all 0.5s;
  -webkit-animation: actionsheet 400ms ease-in-out 0ms;
  animation: actionsheet 400ms ease-in-out 0ms;
  color: var(--emui_text_primary);

  .title {
    font-size: 2rem;
    font-weight: normal;
    height: 2.6rem;
    line-height: 2.6rem;
    padding: 1.5rem 2.4rem 1.5rem 2.4rem;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .message {
    line-height: 2.2rem;
    font-size: 1.6rem;
    margin: 0px 2.4rem;
    color: var(--emui_text_primary);
    padding-bottom: 0.8rem;
  }
}

// 决策理由：使用public.less中定义的actionsheet动画
@keyframes actionsheet {
  0% {
    bottom: -100%;
  }
  to {
    bottom: 1.6rem;
  }
}

.checkbox-container {
  padding: 0.8rem 2.4rem 1.2rem 2.4rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-wrapper {
  position: relative;
  margin-right: 1.2rem;
}

.checkbox-input {
  display: none;
}

.checkbox-core {
  box-sizing: border-box;
  display: inline-block;
  background-color: var(--emui_radio_bg);
  border-radius: 0.4rem;
  border: 1px solid var(--emui_radio_border);
  position: relative;
  width: 2rem;
  height: 2rem;
  vertical-align: middle;
  transition: all 0.2s ease;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 0.6rem;
    height: 1rem;
    border: 2px solid #fff;
    border-top: none;
    border-left: none;
    transform-origin: center;
    transition: all 0.2s ease;
  }
}

.checkbox-input:checked + .checkbox-core {
  background-color: var(--emui_slide_progress_bg);
  border-color: var(--emui_slide_progress_bg);

  &::after {
    transform: translate(-50%, -60%) rotate(45deg) scale(1);
  }
}

.checkbox-text {
  font-size: 1.4rem;
  color: var(--emui_text_primary);
  line-height: 2rem;
}

// 决策理由：使用public.less中定义的dialog-btns样式规范
.dialog-btns {
  display: flex;
  color: var(--emui_accent);
  font-size: 1.6rem;
  height: 5.6rem;
  justify-content: center;
  text-align: center;
  line-height: 5.6rem;
  padding: 0px 1.6rem;
  margin-top: 0.8rem;

  > div {
    flex: 1;
    font-weight: 400;
    height: 4rem;
    width: 14.4rem;
    font-size: 1.6rem;
    text-align: center;
    line-height: 4rem;

    > p {
      width: 100%;
      height: 4rem;
      line-height: 4rem;
      border-radius: 0.3rem;
      margin: 0;
    }
  }
}
</style>
