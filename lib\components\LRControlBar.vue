<template>
  <div class="lr_container" :class="{'disabled' : (disabled && !inside), 'inside' : inside}">
    <div>
      <slot name="left"></slot>
    </div>
    <div>
      <span class="mvalue">{{ value }}</span>
      <span class="mdesc" v-if="desc">{{ desc }}</span>
    </div>
    <div>
      <slot name="right"></slot>
    </div>
    <div class="divider" v-if="divider"></div>
  </div>
</template>

<script>
export default {
  name: 'LRControlBar',
  props: {
    disabled: {
      default: false,
      type: Boolean
    },
    value: {
      default: null,
      type: String
    },
    desc: {
      default: null,
      type: String
    },
    inside: {
      type: Boolean,
      default: false
    },
    divider: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped lang="less">
@import url("../style/public.less");

.lr_container {
  width: 100%;
  .cardStyle();
  position: relative;
  padding: 0px;
  height: 6.4rem;
  display: flex;
  flex-flow: row;

  &.inside {
    margin: 0px;
    padding: 0px;
  }

  &.disabled {
    opacity: .4;
  }

  > div {
    flex: 1;
    flex-shrink: 1;
    display: flex;
    height: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .mvalue {
      font-size: 1.6rem;
      color: var(--emui_text_primary);
    }

    .mdesc {
      margin-top: 0.2rem;
      font-size: 1.2rem;
      white-space: nowrap;
      color: var(--emui_text_secondary);
    }

  }

  .divider {
    position: absolute;
    width: calc(100% - 1.6rem);
    height: 1px;
    bottom: 0;
    left: 0.8rem;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }
}
</style>
