// 导入profile 生成对应state
import profile from './profile';
import { initState } from '../util/index';

const state = {
    // h5_build_time 在build/webpack.prod.conf.js 和 build/webpack.dev.conf.js中的
    // new webpack.DefinePlugin有定义 暴露给全局
    /* eslint-disable */
    h5_build_time,
    /* eslint-enable */
    // 设备型号和sn通过接口获取
    // 获取状态栏返回的高度,默认24,header其余56,共80px,全面屏返回的高度>24
    statusBarHeight: 24,
    navBarHeight: 56,
    // 设备像素比例 为了满足分割线显示真实1px的需求
    DPR: 3,
    isDarkMode: false,
    // 是否已经获取所有数据
    hasGetAllData: false,
    // 显示是否正在下发中的弹窗
    // showTaskToast: false,
    // 手机类型,系统版本相关信息,
    type: 'android',
    version: '10.0',
    homeType: false,
    isPad: false,
    isFold: false, // 用于定制平板竖屏时的margin
    changeScreen: false, // 折叠平展开
    shareDevice: false,
    isShare: false,
    devName: '',
    roomName: '',
    // hilink协议定义 不能修改
    // eslint-disable-next-line id-length
    devId: '',
    sn: '',
    deviceModel: '',
    netStatus: 'online',
    status: 'online', // 设备连接状态
    role: '',
    registerTime: 1753664191491, // 设备注册时间戳
    appMinVersion: '13.0.5.310',// 可以拉起拨号盘的最低app版本号
    servicePhone: '************', // 豪恩服务热线电话号码
    ...initState(profile)
};

export default state;
