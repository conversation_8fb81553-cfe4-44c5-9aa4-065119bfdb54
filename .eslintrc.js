// https://eslint.org/docs/user-guide/configuring

module.exports = {
    root: true,
    parserOptions: {
        parser: 'babel-eslint'
    },
    env: {
        browser: true
    },
    extends: [
        // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
        // consider switching to `plugin:vue/strongly-recommended` or `plugin:vue/recommended` for stricter rules.
        'plugin:vue/essential',
        // https://github.com/standard/standard/blob/master/docs/RULES-en.md
        'standard'
    ],
    // @/assets/api/initApi.js 中会在全局初始化构造函数Time
    globals: {
        "Time": false,
        "Ag": false
    },
    // required to lint *.vue files
    plugins: ['html', 'vue'],
    // add your custom rules here
    rules: {
        // allow async-await
        // ES5代码规范
        'generator-star-spacing': 'off',
        // 统一采取4空格缩进
        // 'no-tabs': 2,
        'no-mixed-spaces-and-tabs': 2,
        indent: [2, 4, { SwitchCase: 1 }],
        // 只检查使用本地声明的变量，但将允许全局变量未被使用。
        // 只有最后一个参数必须使用
        // 缺陷:后面立马使用还是会报错
        'no-unused-vars': [0, { vars: 'local', args: 'after-used', ignoreRestSiblings: false }],

        // 使用Javascript字面量而不是封装基本类
        'no-new-wrappers': 2,

        // 采用括号明确运算的优先级
        'no-mixed-operators': [
            1,
            {
                groups: [
                    ['&', '|', '^', '~', '<<', '>>', '>>>'],
                    ['==', '!=', '===', '!==', '>', '>=', '<', '<='],
                    ['&&', '||'],
                    ['in', 'instanceof']
                ],
                allowSamePrecedence: true
            }
        ],

        // 'line-comment-position': [2],

        // 不在JS代码中使用console.log输出,在非生产模式下都可以,在生产模式下允许使用hLog打印原生的日志
        'no-console': process.env.NODE_ENV === 'production' ? [2, { allow: ['hLog'] }] : 0,

        // 方法的命名，用动词和动宾结构，并采用首字母小写的驼峰命名法
        // 缺陷不能分辨出是否为方法的命名,会验证所有的变量和方法与属性名称
        // 'id-match': [1, '^[a-z]+([A-Z][a-z]+)*$'],

        // 函数名、属性名遵循驼峰风格
        camelcase: 2,

        // 为保证可读性，方法名不宜过长
        'id-length': [1, { min: 1, max: 30 }],

        // 不在JS代码中使用console.log输出
        'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,

        // 不允许空行上的尾随空白
        'no-trailing-spaces': [1, { skipBlankLines: true }],

        // 关闭一个只能声明一个变量
        'one-var': 0,

        // 箭头函数的括号
        'arrow-parens': 0,

        // 禁止隐式转换
        'no-implicit-coercion': 1,

        // 花括号和语句在同一行
        'brace-style': [2, '1tbs'],

        // 判断相等时使用 === 和 !== ，而不是 == 和 !=（重点）
        eqeqeq: 2,

        // 在switch语句的每一个case、和default中都放置一条break语句
        'no-fallthrough': 2,

        // 控制一行的宽度，不要超过120个字符
        'max-len': [1, { code: 120 }],

        // 有替代方案时禁止使用eval方法
        'no-eval': 2,

        // 在不同的概念间（关键字、变量、操作符等）增加空格，以便清楚区分概念（重点）
        'space-before-function-paren': [2, { anonymous: 'always', named: 'never', asyncArrow: 'always' }],

        // 每句代码后必须加";"
        semi: [2, 'always'],

        // 使用let和const代替var
        'no-var': 0,

        // 禁止修改内置对象的原型
        'no-extend-native': 2,

        // 生产环境禁止使用常量作为表达式
        'no-constant-condition': process.env.NODE_ENV === 'production' ? 2 : 0,

        // 在函数内部使用”use strict”
        strict: [2, 'function'],

        // 禁止使用with() {}
        'no-with': 2,

        // 方法的参数个数不宜过多
        'max-params': [2, 7],

        // 不要把方法的入参当做工作变量/临时变量，除非特别需要（重点）
        'no-param-reassign': 2,

        // 方法设计的第一原则是要短小
        'max-statements': [1, 50],

        // 字符串使用单引号优于双引号（重点）
        quotes: [2, 'single'],

        //在花括号前放一个空格（重点）
        'space-before-blocks': 2,

        //在使用长方法链时进行缩进。同时使用前面加点 . 强调这是方法调用而不是新语句。
        'newline-per-chained-call': [0, { ignoreChainWithDepth: 1 }],

        // 控制文件的长度，最好不要超过500行
        'max-lines': [1, 500],

        // 二元和三元操作符操作符始终跟随着前行，如果一行实在放不下，按照上述的缩进风格换行
        // prettier 和 vetur 没有特定的方式
        'operator-linebreak': [2, 'before'],

        // 注释和上面代码块要有空行，注释的//和注释内容要有一个空格
        'spaced-comment': [2, 'always'],

        // 在不同的概念间（关键字、变量、操作符等）增加空格，以便清楚区分概念
        // 逗号后带个空格
        'comma-spacing': [2, { before: false, after: true }]
    }
};
