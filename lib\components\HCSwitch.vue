<template>
  <div class="switch" :class="{'on' : value}">
    <div class="indicator" :class="{'on' : value}"></div>
  </div>
</template>

<script>
export default {
  name: 'HCSwitch',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabled: {
      default: false,
      type: <PERSON><PERSON><PERSON>
    }
  }
};
</script>

<style scoped lang="less">
.switch {
  position: relative;
  width: 3.6rem;
  height: 2.0rem;
  border-radius: 1.6rem;
  background: var(--switch_bar_bg);

  .indicator {
    width: 1.6rem;
    height: 1.6rem;
    background: white;
    position: absolute;
    top: 50%;
    right: 1.8rem;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    border-radius: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    transition: right 0.15s linear;

    &.on {
      right: 0.2rem;
    }
  }

  &.on {
    background: var(--emui_slide_progress_bg)
  }
}

</style>
