<template>
  <div class="list-item-switch-container">
    <div class="left">
      <span class="name">{{ name }}</span>
      <span class="desc">{{ desc }}</span>
    </div>
    <div :id="idStr !== '' ? 'com.huawei.smarthome:id/switch_' + idStr : ''" class="right" @click.stop="handleClick">
      <HCSwitch v-model="active" :disabled="disabled"></HCSwitch>
    </div>
    <div v-if="divider" class="list-item-divider"></div>
  </div>
</template>

<script>
import HCSwitch from '../HCSwitch'

export default {
  name: 'ListItemSwitch',
  components: {
    HCSwitch
  },
  props: {
    name: {
      default: 'Name',
      type: String
    },
    desc: {
      default: null,
      type: String
    },
    active: {
      default: false,
      type: Boolean
    },
    disabled: {
      default: false,
      type: Boolean
    },
    idStr: {
      default: '',
      type: String
    },
    divider: {
      default: true,
      type: Boolean
    },
    idStr: {
      default: '',
      type: String
    }
  },
  data() {
    return {

    }
  },

  mounted() {
  },
  methods: {
    handleClick(e) {
      this.$emit('handleClick');
    },
  }
}
</script>

<style scoped lang="less">
@import url("../../style/public.less");

.list-item-switch-container {
  margin: 0px 1.2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 4.8rem;

  .left {
    // width: 100%;
    max-width: 66%;
    margin: 0.8rem 0;
    display: flex;
    flex-direction: column;
    .name {
      // line-height: 4.8rem;
      font-size: 1.6rem;
      font-weight: 500;
      color: var(--emui_text_primary);
    }
    &> .desc {
      font-size: 1.2rem;
      font-weight: 400;
      color: var(--emui-text_secondary);
    }
  }

  .right {
    // flex: 1;
    display: flex;
    align-items: center;
    padding-left: 1.6rem;
    margin: 0.8rem 0;
  }

  .list-item-divider {
    position: absolute;
    width: 100%;
    height: 1px;
    bottom: 0;
    transform: scaleY(0.25);
    background: var(--emui_color_divider_horizontal);
  }
}
</style>
