@import url("../../lib/style/public.less");

#after-sales-service-page {
    overflow-y: auto;
    background-color: var(--emui_color_subbg);

    .content {
        margin: 6.6rem 0.6rem 0px 0.6rem;
        position: relative;
        padding-bottom: 2.4rem;
    }

    // 产品信息区域
    .product-info-section {
        margin-bottom: 2.4rem;

        .product-info-card {
            .cardStyle();
            padding: 1.6rem;
            display: flex;
            align-items: center;
            .info {
                .product-name {
                    font-size: 1.4rem;
                    font-weight: 500;
                    color: var(--emui_text_primary);
                    margin-right: 1.6rem;
                    flex: 1;
                }

                .product-serial {
                    font-size: 1.2rem;
                    color: var(--emui_text_secondary);
                }
            }

        }
    }

    // 二维码区域
    .qrcode-section {
        margin-bottom: 2.4rem;

        .qrcode-card {
            padding: 0.8rem 7.2rem;
            text-align: center;

            .barcode-container {
                height: 6.6rem;
                display: flex;
                justify-content: center;
                background-color: var(--emui_card_panel_bg);
                canvas {
                    width: 100%;
                    height: 100%;
                }
            }

            .serial-text {
                font-size: 1.2rem;
                color: var(--emui_text_primary);
                margin-bottom: 0.8rem;
                font-family: monospace;
            }

            .qrcode-title {
                font-size: 1.4rem;
                font-weight: 500;
                color: var(--emui_text_primary);
            }

            .qrcode-subtitle {
                font-size: 1.2rem;
                color: var(--emui_text_secondary);
                line-height: 1.4;
            }
        }
    }

    // 咨询服务区域
    .consultation-section {
        margin-bottom: 2.4rem;

        .section-title {
            font-size: 1.2rem;
            color: var(--emui_text_secondary);
            margin-bottom: 1.2rem;
            padding-left: 1.6rem;
        }

        .service-item {
            display: flex;
            align-items: center;
            padding: 1.2rem 0.8rem;
            cursor: pointer;
            position: relative;


            .service-icon {
                width: 2.4rem;
                height: 2.4rem;
                margin-right: 1.2rem;
                flex-shrink: 0;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }

            .service-content {
                flex: 1;

                .service-name {
                    font-size: 1.4rem;
                    color: var(--emui_text_primary);

                }

                .service-desc {
                    margin-top: 0.4rem;
                    font-size: 1.2rem;
                    color: var(--emui_text_secondary);
                }
            }

            .service-arrow {
                width: 1.2rem;
                height: 2.4rem;
                background-image: var(--img_ic_right_arrow);
                background-size: cover;
                background-repeat: no-repeat;
                flex-shrink: 0;
            }
        }

        .service-divider {
            position: absolute;
            width: calc(100% - 4.8rem);
            height: 1px;
            bottom: 0;
            right: 0.8rem;
            transform: scaleY(0.25);
            background: var(--emui_color_divider_horizontal);
        }
    }

    // 第三方服务确认弹窗居中显示
    .dialog {
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        padding: 0px 1.2rem;

        .dialog-container {
            position: relative;
            bottom: 0px;
            left: 0px;
            right: 0px;
            width: auto;
            max-width: 32rem;
        }
    }
}
