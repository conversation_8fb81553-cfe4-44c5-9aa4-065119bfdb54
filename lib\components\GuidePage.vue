<template>
  <div class="guide-page" @touchstart="touchStart"  @touchend="touchEnd">
    <div class="title-area">
      <div class="back-image" @click.stop='cancel(true)'>
      </div>
      <span class="title-text">{{ title }}</span>
    </div>
    <div class="content-area">
        <div class="box" v-for="(item1, index1) in itemList" :key="index1">
          <span class="title">{{ item1.title }}</span>
          <img class="image" :style="{height:imageHeight}" :src="IconImage(item1.img)">
          <div class="content" :style="{textAlign: contentAlign}">
            <span v-for="(item2, index2) in item1.contents" :key="index2">{{ item2 }}</span>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    title: {
      required: true,
      type: String
    },
    itemList: {
      required: true,
      type: Array
    },
    contentAlign: {
      required: false,
      type: String,
      default: "center"
    },
    imageHeight: {
      required: false,
      type: String,
      default: "10rem"
    }
  },
  name: "GuidePage",
  data() {
    return {
      touchStartX: 0, 
      touchStartY: 0, 
      delta: 60
    };
  },  
  computed: {
    ...mapGetters([
        'imgPath'
    ]),
  },
  methods: {
    cancel(check) {
      this.$emit('cancel');
    },
    IconImage(val) {
      if (!val) {
        return '';
      }
      return require(`../../src/assets/${this.imgPath}${val}.png`);
    },
    touchStart(e) {
      console.log("触摸开始")
      this.touchStartX = e.touches[0].clientX;
      this.touchStartY = e.touches[0].clientY;
    },
    touchEnd(e) {
      console.log("触摸结束")
      let deltaX = e.changedTouches[0].clientX - this.touchStartX;
      let deltaY = e.changedTouches[0].clientY - this.touchStartY;
      // X轴的滑动距离大于 delta，且此次事件是以X轴移动为主（左滑或者右滑）;
      console.hLog('deltaX='+deltaX)
      console.hLog('deltaX='+deltaY)
      if (Math.abs(deltaX) > this.delta && Math.abs(deltaX) > Math.abs(deltaY)) {
        if (deltaX > 0) {
          this.$emit('cancel')
        }
      }
    }  
  }
};
</script>

<style lang="less" scoped>
@import url("../style/public.less");
.guide-page {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 2.4rem;
  left: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background-color:var(--emui_color_subbg);
  color: var(--emui_text_primary);

  .title-area {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    height:5.6rem;
    width: 100%;
    align-items:center;
    position: fixed;
    top: 2.4rem;
    left: 0;
    z-index:10;
    background-color:var(--emui_color_subbg);

    .back-image{
      width:2.4rem;
      height:2.4rem;
      margin-left:2.4rem;
      border: 0 none;
      background-size: cover;
      background-repeat: no-repeat;      
      background-image: var(--img_ic_back);
    }
    .title-text{
      font-size: 2rem;
      margin-left:1.6rem;
      font-weight:bold;
      opacity:0.9;
    }
  }

  .content-area{
    margin-top:7.2rem;
    margin-left:2.4rem;
    margin-right:2.4rem;
    margin-bottom:4.7rem;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    background-color:var(--img_ic_back);
    .box{
      margin-bottom:2.4rem;
      display: flex;
      flex-direction: column;

      .title{
        font-size: 1.6rem;
        font-weight:bold;
        opacity:0.9; 
      }
      
      .image{
        margin-top:1.6rem;

        height: 10rem;
        border: 0 none;
        background-size: cover;
        background-repeat: no-repeat;
      }

      .content{
        display: flex;
        flex-direction: column;
        margin-top:1.6rem;
        font-size: 1.4rem;
        opacity:0.6;
      }
    }
  }
}

.pad .guide-page .content-area .box {
  padding-left:8.7rem;
  padding-right:8.7rem;
}

.pad .guide-page .content-area .box .image{
  margin-left:12rem;
  margin-right:12rem;
}

.tahiti .guide-page .content-area .box {
  padding-left:7.1rem;
  padding-right:7.1rem;
}
</style>